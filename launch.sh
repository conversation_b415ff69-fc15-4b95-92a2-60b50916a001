#!/bin/bash

# Launch script for Bandit Wargame GUI

# ANSI color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to detect platform
detect_platform() {
    case "$(uname -s)" in
        Linux*)     echo "linux";;
        Darwin*)    echo "macos";;
        *)          echo "unknown";;
    esac
}

# Check if Python is installed
if ! command_exists "python3"; then
    echo -e "${RED}Python 3 is not installed or not in PATH. Please install Python 3.8 or higher.${NC}"
    exit 1
fi

# Check if Node.js is installed
if ! command_exists "node"; then
    echo -e "${RED}Node.js is not installed or not in PATH. Please install Node.js 14 or higher.${NC}"
    exit 1
fi

# Check if pnpm is installed
if ! command_exists "pnpm"; then
    echo -e "${YELLOW}pnpm is not installed. Installing pnpm globally...${NC}"
    npm install -g pnpm
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to install pnpm. Please install it manually with 'npm install -g pnpm'${NC}"
        exit 1
    fi
fi

# Function to start the backend server
start_backend() {
    local backend_path="bandit-gui-app/backend/bandit-backend"
    
    # Check if virtual environment exists, if not create one
    local venv_path="$backend_path/venv"
    
    # Remove existing venv if it exists to ensure clean state
    if [ -d "$venv_path" ]; then
        rm -rf "$venv_path"
    fi
    
    echo -e "${YELLOW}Creating Python virtual environment...${NC}"
    python3 -m venv "$venv_path"
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to create virtual environment.${NC}"
        exit 1
    fi
    
    # Activate virtual environment and install requirements
    echo -e "${YELLOW}Activating virtual environment and installing requirements...${NC}"
    source "$venv_path/bin/activate"
    
    # Install requirements
    pip install -r "$backend_path/requirements.txt"
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to install Python requirements.${NC}"
        exit 1
    fi
    
    # Detect platform and start backend in new terminal
    local platform=$(detect_platform)
    local current_dir=$(pwd)
    
    if [ "$platform" = "linux" ]; then
        # For Linux using gnome-terminal
        gnome-terminal -- bash -c "cd '$current_dir/$backend_path/src' && source '../venv/bin/activate' && python main.py; exec bash"
    elif [ "$platform" = "macos" ]; then
        # For macOS using osascript
        osascript -e "tell application \"Terminal\" to do script \"cd '$current_dir/$backend_path/src' && source '../venv/bin/activate' && python main.py\""
    else
        echo -e "${RED}Unsupported platform. Please start the backend manually.${NC}"
        echo -e "${YELLOW}Run: cd $backend_path/src && source ../venv/bin/activate && python main.py${NC}"
    fi
    
    # Wait for backend to start
    sleep 5
}

# Function to start the frontend
start_frontend() {
    local frontend_path="bandit-gui-app/frontend/bandit-frontend"
    
    # Install frontend dependencies if node_modules doesn't exist
    if [ ! -d "$frontend_path/node_modules" ]; then
        echo -e "${YELLOW}Installing frontend dependencies...${NC}"
        cd "$frontend_path"
        pnpm install
        if [ $? -ne 0 ]; then
            echo -e "${RED}Failed to install frontend dependencies.${NC}"
            exit 1
        fi
        cd - > /dev/null
    fi
    
    # Detect platform and start frontend in new terminal
    local platform=$(detect_platform)
    local current_dir=$(pwd)
    
    if [ "$platform" = "linux" ]; then
        # For Linux using gnome-terminal
        gnome-terminal -- bash -c "cd '$current_dir/$frontend_path' && pnpm dev; exec bash"
    elif [ "$platform" = "macos" ]; then
        # For macOS using osascript
        osascript -e "tell application \"Terminal\" to do script \"cd '$current_dir/$frontend_path' && pnpm dev\""
    else
        echo -e "${RED}Unsupported platform. Please start the frontend manually.${NC}"
        echo -e "${YELLOW}Run: cd $frontend_path && pnpm dev${NC}"
    fi
}

# Function to open browser
open_browser() {
    local url="http://localhost:5173"
    local platform=$(detect_platform)
    
    echo -e "${YELLOW}Opening browser...${NC}"
    
    if [ "$platform" = "linux" ]; then
        if command_exists "xdg-open"; then
            xdg-open "$url" 2>/dev/null
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}Browser opened successfully.${NC}"
            else
                echo -e "${YELLOW}Could not open browser automatically. Please visit: $url${NC}"
            fi
        else
            echo -e "${YELLOW}xdg-open not available. Please visit: $url${NC}"
        fi
    elif [ "$platform" = "macos" ]; then
        if command_exists "open"; then
            open "$url" 2>/dev/null
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}Browser opened successfully.${NC}"
            else
                echo -e "${YELLOW}Could not open browser automatically. Please visit: $url${NC}"
            fi
        else
            echo -e "${YELLOW}open command not available. Please visit: $url${NC}"
        fi
    else
        echo -e "${YELLOW}Unknown platform. Please manually open your browser and visit: $url${NC}"
    fi
}

# Main execution
echo -e "${CYAN}Starting Bandit Wargame GUI...${NC}"

# Start backend
echo -e "${GREEN}Starting backend server...${NC}"
start_backend

# Start frontend
echo -e "${GREEN}Starting frontend development server...${NC}"
start_frontend

# Wait for frontend to be ready and open browser
echo -e "${YELLOW}Waiting for frontend to be ready...${NC}"
sleep 8
open_browser

echo -e "\n${CYAN}Application is starting up...${NC}"
echo -e "${CYAN}- Backend: http://localhost:5001${NC}"
echo -e "${CYAN}- Frontend: http://localhost:5173${NC}"
echo -e "\n${YELLOW}Note: The frontend might take a moment to start up.${NC}"
