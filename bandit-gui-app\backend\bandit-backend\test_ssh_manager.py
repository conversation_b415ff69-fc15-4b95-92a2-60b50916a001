"""
Tests for the SSH Manager module
"""
import socket
import pytest
from unittest.mock import patch, MagicMock, call
from paramiko import <PERSON><PERSON><PERSON><PERSON><PERSON>, AuthenticationException
from src.ssh_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, SSHConnection

class MockSSHClient:
    def __init__(self):
        self.connect = MagicMock()
        self.exec_command = MagicMock()
        self.close = MagicMock()
        self.invoke_shell = MagicMock()
        self.set_missing_host_key_policy = MagicMock()
        
        # Setup mock transport
        self.transport = MagicMock()
        self.transport.is_active.return_value = True
        self.get_transport = MagicMock(return_value=self.transport)
        
        # Setup mock channel
        self.channel = MagicMock()
        self.channel.recv_ready.return_value = True
        self.channel.recv.return_value = b'test output\n'
        # Setup mock stdin, stdout, stderr
        self.stdin = MagicMock()
        self.stdout = MagicMock()
        self.stderr = MagicMock()
        
        # Setup exec_command return values
        self.exec_command.return_value = (self.stdin, self.stdout, self.stderr)
        self.invoke_shell.return_value = self.channel

@pytest.fixture
def ssh_manager():
    return SSHManager()

@pytest.fixture
def mock_ssh_client():
    return MockSSHClient()

def test_create_connection_success(ssh_manager, mock_ssh_client):
    """Test successful SSH connection creation"""
    with patch('paramiko.SSHClient', return_value=mock_ssh_client):
        session_id = 'test_session'
        result = ssh_manager.create_connection(
            session_id=session_id,
            hostname='test_host',
            port=22,
            username='test_user',
            password='test_pass'
        )
        
        assert result is True
        assert session_id in ssh_manager.connections
        mock_ssh_client.connect.assert_called_once_with(
            hostname='test_host',
            port=22,
            username='test_user',
            password='test_pass',
            timeout=10
        )
        mock_ssh_client.set_missing_host_key_policy.assert_called_once()
        assert ssh_manager.connections[session_id].client == mock_ssh_client
        
        # Verify thread was started
        assert ssh_manager.connections[session_id].read_thread is not None
        assert ssh_manager.connections[session_id].read_thread.is_alive()

def test_create_connection_auth_failure(ssh_manager, mock_ssh_client):
    """Test authentication failure during connection creation"""
    mock_ssh_client.connect.side_effect = AuthenticationException("Authentication failed")
    
    with patch('paramiko.SSHClient', return_value=mock_ssh_client):
        session_id = 'test_session'
        result = ssh_manager.create_connection(
            session_id=session_id,
            hostname='test_host',
            port=22,
            username='test_user',
            password='wrong_pass'
        )
        
        assert result is False
        assert session_id not in ssh_manager.connections


def test_disconnect_session_success(ssh_manager, mock_ssh_client):
    """Test successful session disconnection"""
    with patch('paramiko.SSHClient', return_value=mock_ssh_client):
        session_id = 'test_session'
        ssh_manager.create_connection(
            session_id=session_id,
            hostname='test_host',
            port=22,
            username='test_user',
            password='test_pass'
        )
        
        # Get the connection to check thread state
        connection = ssh_manager.get_connection(session_id)
        read_thread = connection.read_thread
        
        ssh_manager.disconnect_session(session_id)
        
        # Verify client was closed
        mock_ssh_client.close.assert_called_once()
        assert session_id not in ssh_manager.connections
        
        # Verify thread was stopped
        if read_thread:
            read_thread.join(timeout=1)
            assert not read_thread.is_alive()

def test_disconnect_invalid_session(ssh_manager):
    """Test disconnecting a non-existent session"""
    # Should not raise an error for non-existent session
    ssh_manager.disconnect_session('invalid-session')
    assert 'invalid-session' not in ssh_manager.connections


def test_disconnect_all(ssh_manager, mock_ssh_client):
    """Test disconnecting all sessions"""
    with patch('paramiko.SSHClient', return_value=mock_ssh_client):
        # Create multiple sessions
        sessions = []
        connections = []
        for i in range(3):
            session_id = f'test_session_{i}'
            ssh_manager.create_connection(
                session_id=session_id,
                hostname=f'test_host_{i}',
                port=22,
                username='test_user',
                password='test_pass'
            )
            sessions.append(session_id)
            connections.append(ssh_manager.get_connection(session_id))
        
        # Disconnect all sessions
        ssh_manager.disconnect_all()
        
        # All sessions should be closed and removed
        assert mock_ssh_client.close.call_count == 3
        assert len(ssh_manager.connections) == 0
        
        # Verify all threads were stopped
        for conn in connections:
            if conn.read_thread:
                conn.read_thread.join(timeout=1)
                assert not conn.read_thread.is_alive()

def test_get_connection(ssh_manager, mock_ssh_client):
    """Test getting a connection by session ID"""
    with patch('paramiko.SSHClient', return_value=mock_ssh_client):
        session_id = 'test_session'
        ssh_manager.create_connection(
            session_id=session_id,
            hostname='test_host',
            port=22,
            username='test_user',
            password='test_pass'
        )
        
        connection = ssh_manager.get_connection(session_id)
        assert connection is not None
        assert connection.client == mock_ssh_client
        
        # Test non-existent session
        assert ssh_manager.get_connection('non_existent') is None

def test_send_command(ssh_manager, mock_ssh_client):
    """Test sending command to SSH session"""
    with patch('paramiko.SSHClient', return_value=mock_ssh_client):
        session_id = 'test_session'
        ssh_manager.create_connection(
            session_id=session_id,
            hostname='test_host',
            port=22,
            username='test_user',
            password='test_pass'
        )
        
        connection = ssh_manager.get_connection(session_id)
        test_command = 'ls -la'
        connection.send_command(test_command)
        
        # Verify the command was sent through the channel
        mock_ssh_client.invoke_shell.return_value.send.assert_called_once_with(test_command)

def test_output_callback(ssh_manager, mock_ssh_client):
    """Test that output callback receives data"""
    with patch('paramiko.SSHClient', return_value=mock_ssh_client):
        session_id = 'test_session'
        ssh_manager.create_connection(
            session_id=session_id,
            hostname='test_host',
            port=22,
            username='test_user',
            password='test_pass'
        )
        
        connection = ssh_manager.get_connection(session_id)
        callback = MagicMock()
        connection.set_output_callback(callback)
        
        # Simulate receiving data in the channel
        test_output = 'test output\n'
        mock_channel = mock_ssh_client.invoke_shell.return_value
        mock_channel.recv_ready.return_value = True
        mock_channel.recv.return_value = test_output.encode('utf-8')
        
        # Give the thread a moment to process
        import time
        time.sleep(0.1)
        
        # Verify the callback was called with the test output
        callback.assert_called_with(test_output)
        
        # Test set_output_callback
        callback = MagicMock()
        connection.set_output_callback(callback)
        assert connection.output_callback == callback

def test_connection_attributes(ssh_manager, mock_ssh_client):
    """Test connection attributes and methods"""
    with patch('paramiko.SSHClient', return_value=mock_ssh_client):
        session_id = 'test_session'
        ssh_manager.create_connection(
            session_id=session_id,
            hostname='test_host',
            port=22,
            username='test_user',
            password='test_pass'
        )
        
        connection = ssh_manager.get_connection(session_id)
        # Test background thread starts on connect
        assert connection.read_thread is not None
        assert connection.read_thread.is_alive()
        
        # Test disconnect stops the reading thread
        connection.disconnect()
        assert not connection.connected
        mock_ssh_client.close.assert_called_once()
        
        # Wait a moment for thread to stop
        if connection.read_thread:
            connection.read_thread.join(timeout=1)
            assert not connection.read_thread.is_alive()
