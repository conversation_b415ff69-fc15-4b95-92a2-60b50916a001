import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
from pathlib import Path
load_dotenv(Path(__file__).resolve().parents[3] / ".env")

# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, send_from_directory
from flask_socketio import SocketIO
from flask_cors import CORS
from src.models.user import db
from src.routes.user import user_bp
from src.routes.ssh import ssh_bp, init_socketio
from src.routes.levels import levels_bp
from src.routes.mentor import mentor_bp, init_mentor_socketio
from src.routes.health import health_bp

app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'static'))
app.config['SECRET_KEY'] = 'asdf#FGSgvasgf$5$WGT'

# Enable CORS for all routes
CORS(app, origins="*")

# Initialize SocketIO
socketio = SocketIO(app, cors_allowed_origins="*")

app.register_blueprint(user_bp, url_prefix='/api')
app.register_blueprint(ssh_bp, url_prefix='/api/ssh')
app.register_blueprint(levels_bp, url_prefix='/api/levels')
app.register_blueprint(mentor_bp, url_prefix='/api/mentor')
app.register_blueprint(health_bp)

# Initialize SSH SocketIO events
init_socketio(socketio)

# Initialize Mentor SocketIO events
init_mentor_socketio(socketio)

# uncomment if you need to use database
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(__file__), 'database', 'app.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)
with app.app_context():
    db.create_all()

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
            return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404


if __name__ == '__main__':
    socketio.run(app, host='0.0.0.0', port=5001, debug=True)
