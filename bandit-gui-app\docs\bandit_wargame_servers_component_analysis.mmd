graph TD

    terminal-69983580b1be33b446c4ee837bdf434c["Terminal"]
    terminal-78d119381d84ebd0cea743684a30dac2["Terminal"]
    terminal-d13861cdf47babc131626e83fcdf70ab["Terminal"]
    subgraph search-results-group-d13861cdf47babc131626e83fcdf70ab["Search Results"]
        search-result-d13861cdf47babc131626e83fcdf70ab["search-result-d13861cdf47babc131626e83fcdf70ab"]
        report-section-d13861cdf47babc131626e83fcdf70ab-0["report-section-d13861cdf47babc131626e83fcdf70ab-0"]
        report-section-d13861cdf47babc131626e83fcdf70ab-1["report-section-d13861cdf47babc131626e83fcdf70ab-1"]
        report-section-d13861cdf47babc131626e83fcdf70ab-2["report-section-d13861cdf47babc131626e83fcdf70ab-2"]
        report-section-d13861cdf47babc131626e83fcdf70ab-16["report-section-d13861cdf47babc131626e83fcdf70ab-16"]
        subgraph section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture-wrapper["High-Level Architecture"]
            aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture["AI Mentor<br>Service"]
            banditBackend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture["Bandit Backend API<br>Flask Application"]
            banditWargame_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture["Bandit Wargame<br>Game Server"]
            frontendApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture["Frontend App<br>React"]
            sqliteDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture["SQLite Database<br>app.db"]
            %% Edges at this level (grouped by source)
            frontendApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture["Frontend App<br>React"] -->|API Calls| banditBackend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture["Bandit Backend API<br>Flask Application"]
            banditBackend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture["Bandit Backend API<br>Flask Application"] -->|SSH| banditWargame_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture["Bandit Wargame<br>Game Server"]
            banditBackend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture["Bandit Backend API<br>Flask Application"] -->|Manages| sqliteDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture["SQLite Database<br>app.db"]
            banditBackend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture["Bandit Backend API<br>Flask Application"] -->|Integrates| aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture["AI Mentor<br>Service"]
        end
        subgraph section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper["Mid-Level Component Breakdown"]
            mainApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Main Application<br>Flask Entry Point"]
            subgraph APIRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["API Routes<br>Flask Blueprints"]
                levelsRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Levels Routes<br>API Module"]
                mentorRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Mentor Routes<br>API Module"]
                sshRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["SSH Routes<br>API Module"]
                userRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["User Routes<br>API Module"]
            end
            subgraph CoreLogic_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Core Logic Modules<br>Python Classes"]
                aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["AI Mentor<br>AI Integration"]
                levelScraper_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Level Scraper<br>Data Extraction"]
                sshManager_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["SSH Manager<br>SSH Client"]
            end
            subgraph DatabaseModels_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Database and Models<br>SQLAlchemy"]
                appDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["app.db<br>SQLite Database"]
                userModel_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["User Model<br>SQLAlchemy ORM"]
                %% Edges at this level (grouped by source)
                userModel_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["User Model<br>SQLAlchemy ORM"] -->|Interacts with| appDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["app.db<br>SQLite Database"]
            end
            %% Edges at this level (grouped by source)
            mainApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Main Application<br>Flask Entry Point"] -->|Registers| sshRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["SSH Routes<br>API Module"]
            mainApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Main Application<br>Flask Entry Point"] -->|Registers| mentorRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Mentor Routes<br>API Module"]
            mainApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Main Application<br>Flask Entry Point"] -->|Registers| levelsRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Levels Routes<br>API Module"]
            mainApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Main Application<br>Flask Entry Point"] -->|Registers| userRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["User Routes<br>API Module"]
            mainApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Main Application<br>Flask Entry Point"] -->|Initializes| appDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["app.db<br>SQLite Database"]
            sshRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["SSH Routes<br>API Module"] -->|Uses| sshManager_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["SSH Manager<br>SSH Client"]
            mentorRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Mentor Routes<br>API Module"] -->|Uses| aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["AI Mentor<br>AI Integration"]
            levelsRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Levels Routes<br>API Module"] -->|Uses| levelScraper_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["Level Scraper<br>Data Extraction"]
            userRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["User Routes<br>API Module"] -->|Uses| userModel_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown["User Model<br>SQLAlchemy ORM"]
        end
        subgraph section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper["Implementation Details"]
            aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["AIMentor<br>AI Integration"]
            appDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["app.db<br>SQLite Database"]
            backend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["Bandit Backend<br>Flask App"]
            banditWargame_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["Bandit Wargame<br>Game Server"]
            externalAI_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["External AI API<br>Service"]
            frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["Frontend App<br>GUI"]
            mentorHintRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["/api/mentor/hint<br>API Endpoint"]
            sshExecuteRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["/api/ssh/execute<br>API Endpoint"]
            sshManager_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["SSHManager<br>SSH Client"]
            userModel_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["User Model<br>SQLAlchemy ORM"]
            userRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["/api/user/login<br>API Endpoint"]
            %% Edges at this level (grouped by source)
            frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["Frontend App<br>GUI"] -->|POST /api/user/login| userRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["/api/user/login<br>API Endpoint"]
            frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["Frontend App<br>GUI"] -->|POST /api/ssh/execute| sshExecuteRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["/api/ssh/execute<br>API Endpoint"]
            frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["Frontend App<br>GUI"] -->|POST /api/mentor/hint| mentorHintRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["/api/mentor/hint<br>API Endpoint"]
            userRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["/api/user/login<br>API Endpoint"] -->|Validates against| userModel_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["User Model<br>SQLAlchemy ORM"]
            userRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["/api/user/login<br>API Endpoint"] -->|Returns response| frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["Frontend App<br>GUI"]
            userModel_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["User Model<br>SQLAlchemy ORM"] -->|Queries| appDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["app.db<br>SQLite Database"]
            sshExecuteRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["/api/ssh/execute<br>API Endpoint"] -->|Calls execute_command| sshManager_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["SSHManager<br>SSH Client"]
            sshExecuteRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["/api/ssh/execute<br>API Endpoint"] -->|Returns output| frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["Frontend App<br>GUI"]
            sshManager_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["SSHManager<br>SSH Client"] -->|Executes command via Paramiko| banditWargame_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["Bandit Wargame<br>Game Server"]
            mentorHintRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["/api/mentor/hint<br>API Endpoint"] -->|Calls get_hint| aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["AIMentor<br>AI Integration"]
            mentorHintRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["/api/mentor/hint<br>API Endpoint"] -->|Returns hint| frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["Frontend App<br>GUI"]
            aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["AIMentor<br>AI Integration"] -->|Interacts with| externalAI_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details["External AI API<br>Service"]
        end
        %% Edges at this level (grouped by source)
        report-section-d13861cdf47babc131626e83fcdf70ab-1["report-section-d13861cdf47babc131626e83fcdf70ab-1"] -->|Diagram| section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture-wrapper["High-Level Architecture"]
        report-section-d13861cdf47babc131626e83fcdf70ab-2["report-section-d13861cdf47babc131626e83fcdf70ab-2"] -->|Diagram| section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper["Mid-Level Component Breakdown"]
        report-section-d13861cdf47babc131626e83fcdf70ab-16["report-section-d13861cdf47babc131626e83fcdf70ab-16"] -->|Diagram| section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper["Implementation Details"]
        search-result-d13861cdf47babc131626e83fcdf70ab["search-result-d13861cdf47babc131626e83fcdf70ab"] --> report-section-d13861cdf47babc131626e83fcdf70ab-0["report-section-d13861cdf47babc131626e83fcdf70ab-0"]
        report-section-d13861cdf47babc131626e83fcdf70ab-0["report-section-d13861cdf47babc131626e83fcdf70ab-0"] --> report-section-d13861cdf47babc131626e83fcdf70ab-1["report-section-d13861cdf47babc131626e83fcdf70ab-1"]
        report-section-d13861cdf47babc131626e83fcdf70ab-0["report-section-d13861cdf47babc131626e83fcdf70ab-0"] --> report-section-d13861cdf47babc131626e83fcdf70ab-2["report-section-d13861cdf47babc131626e83fcdf70ab-2"]
        report-section-d13861cdf47babc131626e83fcdf70ab-0["report-section-d13861cdf47babc131626e83fcdf70ab-0"] --> report-section-d13861cdf47babc131626e83fcdf70ab-16["report-section-d13861cdf47babc131626e83fcdf70ab-16"]
    end
    subgraph search-results-group-78d119381d84ebd0cea743684a30dac2["Search Results"]
        search-result-78d119381d84ebd0cea743684a30dac2["search-result-78d119381d84ebd0cea743684a30dac2"]
        report-section-78d119381d84ebd0cea743684a30dac2-0["report-section-78d119381d84ebd0cea743684a30dac2-0"]
        report-section-78d119381d84ebd0cea743684a30dac2-1["report-section-78d119381d84ebd0cea743684a30dac2-1"]
        report-section-78d119381d84ebd0cea743684a30dac2-2["report-section-78d119381d84ebd0cea743684a30dac2-2"]
        report-section-78d119381d84ebd0cea743684a30dac2-5["report-section-78d119381d84ebd0cea743684a30dac2-5"]
        report-section-78d119381d84ebd0cea743684a30dac2-10["report-section-78d119381d84ebd0cea743684a30dac2-10"]
        subgraph section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture-wrapper["High-Level Architecture"]
            aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture["AI Mentor Module<br>Python"]
            backend_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture["Backend<br>Python Application"]
            frontend_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture["Frontend<br>Application"]
            mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture["Mentor Routes Module<br>Python"]
            %% Edges at this level (grouped by source)
            frontend_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture["Frontend<br>Application"] -->|Requests| backend_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture["Backend<br>Python Application"]
            backend_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture["Backend<br>Python Application"] -->|Encapsulates| aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture["AI Mentor Module<br>Python"]
            backend_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture["Backend<br>Python Application"] -->|Exposes via API| mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture["Mentor Routes Module<br>Python"]
            mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture["Mentor Routes Module<br>Python"] -->|Utilizes| aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture["AI Mentor Module<br>Python"]
        end
        subgraph section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper["Mid-Level Component Interaction"]
            aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["ai_mentor Module<br>Python"]
            externalAILib_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["External AI Library<br>OpenAI Client"]
            frontendApp_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["Frontend Application<br>React"]
            httpLibs_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["HTTP Libraries<br>httpx/requests"]
            mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["mentor Routes Module<br>Python"]
            webFramework_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["Web Framework<br>Flask"]
            %% Edges at this level (grouped by source)
            aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["ai_mentor Module<br>Python"] -->|Uses| externalAILib_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["External AI Library<br>OpenAI Client"]
            aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["ai_mentor Module<br>Python"] -->|Uses| httpLibs_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["HTTP Libraries<br>httpx/requests"]
            mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["mentor Routes Module<br>Python"] -->|Utilizes| aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["ai_mentor Module<br>Python"]
            mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["mentor Routes Module<br>Python"] -->|Depends on| webFramework_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["Web Framework<br>Flask"]
            frontendApp_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["Frontend Application<br>React"] -->|Consumes API| mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction["mentor Routes Module<br>Python"]
        end
        subgraph section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details-wrapper["Low-Level Implementation Details"]
            subgraph aiMentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["ai_mentor.py Implementation"]
                banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["BanditAIMentor<br>Class"]
                clearConversationMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["clear_conversation()<br>Method"]
                explainCommandMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["explain_command()<br>Method"]
                getLevelHintMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["get_level_hint()<br>Method"]
                getResponseMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["get_response()<br>Method"]
                globalInstance_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["ai_mentor<br>Global Instance"]
                initMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["__init__()<br>Method"]
                %% Edges at this level (grouped by source)
                banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["BanditAIMentor<br>Class"] -->|Initializes| initMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["__init__()<br>Method"]
                banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["BanditAIMentor<br>Class"] -->|Uses OpenAI API| getResponseMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["get_response()<br>Method"]
                banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["BanditAIMentor<br>Class"] -->|Manages history| clearConversationMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["clear_conversation()<br>Method"]
                banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["BanditAIMentor<br>Class"] -->|Provides static hint| getLevelHintMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["get_level_hint()<br>Method"]
                banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["BanditAIMentor<br>Class"] -->|Provides static explanation| explainCommandMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["explain_command()<br>Method"]
                globalInstance_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["ai_mentor<br>Global Instance"] -->|Is instance of| banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["BanditAIMentor<br>Class"]
            end
            subgraph mentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["mentor.py Implementation"]
                chatRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["/chat<br>POST Route"]
                clearRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["/clear/<br>POST Route"]
                explainRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["/explain/<br>GET Route"]
                hintRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["/hint/<br>GET Route"]
                initSocketIOFunc_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["init_mentor_socketio()<br>Function"]
                mentorBlueprint_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["mentor_bp<br>Flask Blueprint"]
                socketIOClear_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["@socketio.on('mentor_clear')<br>Event Handler"]
                socketIOMessage_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["@socketio.on('mentor_message')<br>Event Handler"]
                %% Edges at this level (grouped by source)
                initSocketIOFunc_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["init_mentor_socketio()<br>Function"] -->|Registers| socketIOMessage_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["@socketio.on('mentor_message')<br>Event Handler"]
                initSocketIOFunc_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["init_mentor_socketio()<br>Function"] -->|Registers| socketIOClear_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["@socketio.on('mentor_clear')<br>Event Handler"]
                mentorBlueprint_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["mentor_bp<br>Flask Blueprint"] -->|Defines| hintRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["/hint/<br>GET Route"]
                mentorBlueprint_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["mentor_bp<br>Flask Blueprint"] -->|Defines| explainRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["/explain/<br>GET Route"]
                mentorBlueprint_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["mentor_bp<br>Flask Blueprint"] -->|Defines| chatRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["/chat<br>POST Route"]
                mentorBlueprint_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["mentor_bp<br>Flask Blueprint"] -->|Defines| clearRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["/clear/<br>POST Route"]
            end
            %% Edges at this level (grouped by source)
            socketIOMessage_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["@socketio.on('mentor_message')<br>Event Handler"] -->|Calls| getResponseMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["get_response()<br>Method"]
            socketIOClear_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["@socketio.on('mentor_clear')<br>Event Handler"] -->|Calls| clearConversationMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["clear_conversation()<br>Method"]
            hintRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["/hint/<br>GET Route"] -->|Calls| getLevelHintMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["get_level_hint()<br>Method"]
            explainRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["/explain/<br>GET Route"] -->|Calls| explainCommandMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["explain_command()<br>Method"]
            chatRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["/chat<br>POST Route"] -->|Calls| getResponseMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["get_response()<br>Method"]
            clearRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["/clear/<br>POST Route"] -->|Calls| clearConversationMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details["clear_conversation()<br>Method"]
        end
        subgraph section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper["Conclusion"]
            banditAIMentor_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["BanditAIMentor<br>Core AI Logic"]
            frontendApp_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["Frontend Application<br>User Interface"]
            mentorRoutes_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["Mentor Routes<br>API Endpoints"]
            openAIApi_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["OpenAI API<br>External Service"]
            restApi_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["REST API<br>HTTP Communication"]
            socketIO_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["SocketIO<br>Real-time Communication"]
            %% Edges at this level (grouped by source)
            banditAIMentor_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["BanditAIMentor<br>Core AI Logic"] -->|Interacts with| openAIApi_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["OpenAI API<br>External Service"]
            mentorRoutes_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["Mentor Routes<br>API Endpoints"] -->|Exposes via| socketIO_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["SocketIO<br>Real-time Communication"]
            mentorRoutes_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["Mentor Routes<br>API Endpoints"] -->|Exposes via| restApi_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["REST API<br>HTTP Communication"]
            mentorRoutes_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["Mentor Routes<br>API Endpoints"] -->|Utilizes| banditAIMentor_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["BanditAIMentor<br>Core AI Logic"]
            frontendApp_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["Frontend Application<br>User Interface"] -->|Communicates via| socketIO_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["SocketIO<br>Real-time Communication"]
            frontendApp_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["Frontend Application<br>User Interface"] -->|Communicates via| restApi_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["REST API<br>HTTP Communication"]
            socketIO_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["SocketIO<br>Real-time Communication"] -->|Provides chat| banditAIMentor_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["BanditAIMentor<br>Core AI Logic"]
            restApi_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["REST API<br>HTTP Communication"] -->|Provides hints/chat| banditAIMentor_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion["BanditAIMentor<br>Core AI Logic"]
        end
        %% Edges at this level (grouped by source)
        report-section-78d119381d84ebd0cea743684a30dac2-1["report-section-78d119381d84ebd0cea743684a30dac2-1"] -->|Diagram| section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture-wrapper["High-Level Architecture"]
        report-section-78d119381d84ebd0cea743684a30dac2-2["report-section-78d119381d84ebd0cea743684a30dac2-2"] -->|Diagram| section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper["Mid-Level Component Interaction"]
        report-section-78d119381d84ebd0cea743684a30dac2-5["report-section-78d119381d84ebd0cea743684a30dac2-5"] -->|Diagram| section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details-wrapper["Low-Level Implementation Details"]
        report-section-78d119381d84ebd0cea743684a30dac2-10["report-section-78d119381d84ebd0cea743684a30dac2-10"] -->|Diagram| section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper["Conclusion"]
        search-result-78d119381d84ebd0cea743684a30dac2["search-result-78d119381d84ebd0cea743684a30dac2"] --> report-section-78d119381d84ebd0cea743684a30dac2-0["report-section-78d119381d84ebd0cea743684a30dac2-0"]
        report-section-78d119381d84ebd0cea743684a30dac2-0["report-section-78d119381d84ebd0cea743684a30dac2-0"] --> report-section-78d119381d84ebd0cea743684a30dac2-1["report-section-78d119381d84ebd0cea743684a30dac2-1"]
        report-section-78d119381d84ebd0cea743684a30dac2-0["report-section-78d119381d84ebd0cea743684a30dac2-0"] --> report-section-78d119381d84ebd0cea743684a30dac2-2["report-section-78d119381d84ebd0cea743684a30dac2-2"]
        report-section-78d119381d84ebd0cea743684a30dac2-0["report-section-78d119381d84ebd0cea743684a30dac2-0"] --> report-section-78d119381d84ebd0cea743684a30dac2-5["report-section-78d119381d84ebd0cea743684a30dac2-5"]
        report-section-78d119381d84ebd0cea743684a30dac2-0["report-section-78d119381d84ebd0cea743684a30dac2-0"] --> report-section-78d119381d84ebd0cea743684a30dac2-10["report-section-78d119381d84ebd0cea743684a30dac2-10"]
    end
    subgraph search-results-group-69983580b1be33b446c4ee837bdf434c["Search Results"]
        search-result-69983580b1be33b446c4ee837bdf434c["search-result-69983580b1be33b446c4ee837bdf434c"]
        report-section-69983580b1be33b446c4ee837bdf434c-0["report-section-69983580b1be33b446c4ee837bdf434c-0"]
        report-section-69983580b1be33b446c4ee837bdf434c-1["report-section-69983580b1be33b446c4ee837bdf434c-1"]
        report-section-69983580b1be33b446c4ee837bdf434c-2["report-section-69983580b1be33b446c4ee837bdf434c-2"]
        subgraph section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper["High-Level Architecture"]
            aiMentor_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["AI Mentor Capabilities<br>Service"]
            backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Backend Application<br>Node.js/Python"]
            banditServers_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Bandit Wargame Servers<br>SSH Target"]
            frontendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Frontend Application<br>React"]
            %% Edges at this level (grouped by source)
            frontendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Frontend Application<br>React"] -->|WebSockets| backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Backend Application<br>Node.js/Python"]
            backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Backend Application<br>Node.js/Python"] -->|SSH Connection| banditServers_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Bandit Wargame Servers<br>SSH Target"]
            backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Backend Application<br>Node.js/Python"] -->|Provides| aiMentor_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["AI Mentor Capabilities<br>Service"]
        end
        subgraph section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper["Core Application Component: [App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)"]
            aiMentorComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["AIMentor Component<br>React Component"]
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"]
            backendServer_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["Backend Server<br>Node.js/Socket.IO"]
            levelInfo_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["LevelInfo Component<br>React Component"]
            sshLogin_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["SSHLogin Component<br>React Component"]
            terminalComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["Terminal Component<br>xterm.js Wrapper"]
            webSocketClient_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["WebSocket Client<br>socket.io-client"]
            xtermJs_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["xterm.js Library<br>Terminal Emulator"]
            %% Edges at this level (grouped by source)
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"] -->|Manages State| webSocketClient_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["WebSocket Client<br>socket.io-client"]
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"] -->|Renders| sshLogin_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["SSHLogin Component<br>React Component"]
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"] -->|Renders| levelInfo_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["LevelInfo Component<br>React Component"]
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"] -->|Renders| aiMentorComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["AIMentor Component<br>React Component"]
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"] -->|Renders| terminalComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["Terminal Component<br>xterm.js Wrapper"]
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"] -->|Emits/Listens Events| backendServer_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["Backend Server<br>Node.js/Socket.IO"]
            webSocketClient_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["WebSocket Client<br>socket.io-client"] -->|Connects to| backendServer_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["Backend Server<br>Node.js/Socket.IO"]
            sshLogin_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["SSHLogin Component<br>React Component"] -->|Triggers SSH Connect| appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"]
            terminalComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["Terminal Component<br>xterm.js Wrapper"] -->|Uses| xtermJs_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["xterm.js Library<br>Terminal Emulator"]
        end
        %% Edges at this level (grouped by source)
        report-section-69983580b1be33b446c4ee837bdf434c-1["report-section-69983580b1be33b446c4ee837bdf434c-1"] -->|Diagram| section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper["High-Level Architecture"]
        report-section-69983580b1be33b446c4ee837bdf434c-2["report-section-69983580b1be33b446c4ee837bdf434c-2"] -->|Diagram| section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper["Core Application Component: [App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)"]
        search-result-69983580b1be33b446c4ee837bdf434c["search-result-69983580b1be33b446c4ee837bdf434c"] --> report-section-69983580b1be33b446c4ee837bdf434c-0["report-section-69983580b1be33b446c4ee837bdf434c-0"]
        report-section-69983580b1be33b446c4ee837bdf434c-0["report-section-69983580b1be33b446c4ee837bdf434c-0"] --> report-section-69983580b1be33b446c4ee837bdf434c-1["report-section-69983580b1be33b446c4ee837bdf434c-1"]
        report-section-69983580b1be33b446c4ee837bdf434c-0["report-section-69983580b1be33b446c4ee837bdf434c-0"] --> report-section-69983580b1be33b446c4ee837bdf434c-2["report-section-69983580b1be33b446c4ee837bdf434c-2"]
    end
    %% Edges at this level (grouped by source)
    search-results-group-69983580b1be33b446c4ee837bdf434c["Search Results"] -->|Provides context to| terminal-69983580b1be33b446c4ee837bdf434c["Terminal"]
    search-results-group-78d119381d84ebd0cea743684a30dac2["Search Results"] -->|Provides context to| terminal-78d119381d84ebd0cea743684a30dac2["Terminal"]
    search-results-group-d13861cdf47babc131626e83fcdf70ab["Search Results"] -->|Provides context to| terminal-d13861cdf47babc131626e83fcdf70ab["Terminal"]
