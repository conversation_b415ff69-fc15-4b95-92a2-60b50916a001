from flask import Blueprint, jsonify, request
from src.level_scraper import BanditLevelScraper
import os
import json

levels_bp = Blueprint('levels', __name__)

# Initialize scraper
scraper = BanditLevelScraper()
levels_data_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'bandit_levels.json')

def ensure_data_directory():
    """Ensure data directory exists"""
    data_dir = os.path.dirname(levels_data_file)
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

def load_levels_data():
    """Load levels data from file or scrape if not exists"""
    ensure_data_directory()
    
    if os.path.exists(levels_data_file):
        try:
            with open(levels_data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading levels data: {e}")
    
    # If file doesn't exist or loading failed, scrape data
    print("Scraping levels data...")
    levels_data = scraper.scrape_all_levels(30)
    
    # Save to file
    try:
        with open(levels_data_file, 'w', encoding='utf-8') as f:
            json.dump(levels_data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"Error saving levels data: {e}")
    
    return levels_data

@levels_bp.route('/all', methods=['GET'])
def get_all_levels():
    """Get all available levels"""
    try:
        levels_data = load_levels_data()
        return jsonify({
            'success': True,
            'levels': levels_data,
            'count': len(levels_data)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@levels_bp.route('/<int:level_num>', methods=['GET'])
def get_level(level_num):
    """Get specific level data"""
    try:
        levels_data = load_levels_data()
        
        if str(level_num) in levels_data:
            return jsonify({
                'success': True,
                'level': levels_data[str(level_num)]
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Level {level_num} not found'
            }), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@levels_bp.route('/refresh', methods=['POST'])
def refresh_levels():
    """Refresh levels data by scraping again"""
    try:
        print("Refreshing levels data...")
        levels_data = scraper.scrape_all_levels(30)
        
        # Save to file
        ensure_data_directory()
        with open(levels_data_file, 'w', encoding='utf-8') as f:
            json.dump(levels_data, f, indent=2, ensure_ascii=False)
        
        return jsonify({
            'success': True,
            'message': 'Levels data refreshed successfully',
            'count': len(levels_data)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@levels_bp.route('/search', methods=['GET'])
def search_levels():
    """Search levels by keyword"""
    try:
        query = request.args.get('q', '').lower()
        if not query:
            return jsonify({
                'success': False,
                'error': 'Query parameter "q" is required'
            }), 400
        
        levels_data = load_levels_data()
        matching_levels = {}
        
        for level_key, level_data in levels_data.items():
            # Search in title, goal, and commands
            searchable_text = (
                level_data.get('title', '') + ' ' +
                level_data.get('goal', '') + ' ' +
                ' '.join(level_data.get('commands', []))
            ).lower()
            
            if query in searchable_text:
                matching_levels[level_key] = level_data
        
        return jsonify({
            'success': True,
            'levels': matching_levels,
            'count': len(matching_levels),
            'query': query
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@levels_bp.route('/detect', methods=['POST'])
def detect_current_level():
    """Detect current level based on terminal output or directory"""
    try:
        data = request.get_json()
        terminal_output = data.get('terminal_output', '')
        current_directory = data.get('current_directory', '')
        
        # Simple level detection logic
        detected_level = 0
        
        # Look for bandit user in terminal output
        if 'bandit' in terminal_output.lower():
            import re
            # Try to extract bandit level from username
            match = re.search(r'bandit(\d+)', terminal_output.lower())
            if match:
                detected_level = int(match.group(1))
        
        # Alternative: detect from current directory
        if '/home/<USER>' in current_directory:
            match = re.search(r'/home/<USER>', current_directory)
            if match:
                detected_level = int(match.group(1))
        
        return jsonify({
            'success': True,
            'detected_level': detected_level,
            'confidence': 'high' if detected_level > 0 else 'low'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

