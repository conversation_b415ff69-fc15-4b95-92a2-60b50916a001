# Contributing to Bandit Wargame GUI

First off, thank you for considering contributing to the Bandit Wargame GUI! We appreciate your time and effort in helping to improve this project.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Making Changes](#making-changes)
- [Code Style](#code-style)
- [Testing](#testing)
- [Pull Requests](#pull-requests)
- [Reporting Issues](#reporting-issues)
- [Feature Requests](#feature-requests)
- [Documentation](#documentation)
- [Community](#community)

## Code of Conduct

This project and everyone participating in it is governed by our [Code of Conduct](CODE_OF_CONDUCT.md). By participating, you are expected to uphold this code. Please report any unacceptable behavior to the project maintainers.

## Getting Started

1. **Fork the repository** on GitHub
2. **Clone your fork** locally
   ```bash
   git clone https://github.com/your-username/bandit-wargame-gui.git
   cd bandit-wargame-gui
   ```
3. **Set up the development environment** (see [Development Setup](#development-setup))
4. **Create a branch** for your changes
   ```bash
   git checkout -b feature/your-feature-name
   ```

## Development Setup

### Prerequisites

- Python 3.11+
- Node.js 20+
- pnpm
- Git

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd bandit-gui-app/backend/bandit-backend
   ```

2. Create and activate a virtual environment:
   ```bash
   # On Windows
   python -m venv venv
   .\venv\Scripts\activate
   
   # On macOS/Linux
   python3 -m venv venv
   source venv/bin/activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements-dev.txt
   ```

4. Set up environment variables:
   The application uses a single `.env` file in the repository root. Copy the sample file and update it with your configuration:
   ```bash
   cp ../../../.env.sample ../../.env
   # Edit ../../.env with your configuration
   ```

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd ../../frontend/bandit-frontend
   ```

2. Install dependencies:
   ```bash
   pnpm install
   ```

3. Environment variables are configured in the repository root `.env` file (already set up in the backend setup step). No additional setup is needed for the frontend.

### Running the Application

1. Start the backend server:
   ```bash
   cd bandit-gui-app/backend/bandit-backend
   python src/main.py
   ```

2. In a new terminal, start the frontend development server:
   ```bash
   cd bandit-gui-app/frontend/bandit-frontend
   pnpm run dev
   ```

3. Open your browser to `http://localhost:5173`

## Development Workflow

1. **Sync your fork** with the main repository:
   ```bash
   git remote add upstream https://github.com/original-owner/bandit-wargame-gui.git
   git fetch upstream
   git checkout main
   git merge upstream/main
   ```

2. **Create a feature branch** for your changes:
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make your changes** following the [code style](#code-style) guidelines

4. **Run tests** to ensure nothing is broken

5. **Commit your changes** with a descriptive message:
   ```bash
   git add .
   git commit -m "feat: add new feature"
   ```

6. **Push your changes** to your fork:
   ```bash
   git push -u origin feature/your-feature-name
   ```

7. **Open a Pull Request** on GitHub

## Making Changes

### Backend Changes

- Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/) style guide
- Use type hints for all function parameters and return values
- Write docstrings for all public functions and classes
- Include unit tests for new features and bug fixes
- Update API documentation in `API_REFERENCE.md` if adding/changing endpoints

### Frontend Changes

- Follow the existing React component structure
- Use functional components with hooks
- Follow the [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
- Use Tailwind CSS for styling
- Ensure responsive design works on all screen sizes
- Update documentation if adding/changing components or features

## Code Style

### Python

- Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/)
- Use Black for code formatting
- Use isort for import sorting
- Maximum line length: 88 characters
- Use double quotes for strings
- Use type hints for all functions and methods

### JavaScript/TypeScript

- Use Prettier for code formatting
- Use ESLint for linting
- Use camelCase for variables and functions
- Use PascalCase for component names
- Use meaningful variable and function names

## Testing

### Backend Tests

Run the test suite:

```bash
cd bandit-gui-app/backend/bandit-backend
pytest
```

### Frontend Tests

Run the test suite:

```bash
cd bandit-gui-app/frontend/bandit-frontend
pnpm test
```

### Test Coverage

We aim for at least 80% test coverage. You can check the coverage with:

```bash
# Backend
cd bandit-gui-app/backend/bandit-backend
pytest --cov=src --cov-report=term-missing

# Frontend
cd ../../frontend/bandit-frontend
pnpm test -- --coverage
```

## Pull Requests

1. **Fork the repository** and create your branch from `main`
2. **Make your changes** following the coding standards
3. **Add tests** for any new features or bug fixes
4. **Update documentation** as needed
5. **Run tests** to ensure everything passes
6. **Submit a pull request** with a clear description of your changes

### Pull Request Guidelines

- Keep pull requests focused on a single feature or bug fix
- Write a clear, concise pull request description
- Reference any related issues in your pull request
- Ensure all tests pass before submitting
- Request review from at least one maintainer

## Reporting Issues

Found a bug? Please let us know by [opening an issue](https://github.com/your-username/bandit-wargame-gui/issues/new).

### Bug Report Template

```markdown
## Describe the Bug
A clear and concise description of what the bug is.

## To Reproduce
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## Expected Behavior
A clear and concise description of what you expected to happen.

## Screenshots
If applicable, add screenshots to help explain your problem.

## Environment (please complete the following information):
- OS: [e.g., Windows 10, macOS 12.0, Ubuntu 20.04]
- Browser: [e.g., Chrome 98, Firefox 97, Safari 15]
- Version: [e.g., 1.0.0]

## Additional Context
Add any other context about the problem here.
```

## Feature Requests

Have an idea for a new feature? Please [open an issue](https://github.com/your-username/bandit-wargame-gui/issues/new) to discuss it.

### Feature Request Template

```markdown
## Is your feature request related to a problem? Please describe.
A clear and concise description of what the problem is.

## Describe the solution you'd like
A clear and concise description of what you want to happen.

## Describe alternatives you've considered
A clear and concise description of any alternative solutions or features you've considered.

## Additional context
Add any other context or screenshots about the feature request here.
```

## Documentation

Good documentation is crucial for the success of any open-source project. We appreciate contributions to:

- Code documentation (docstrings, comments)
- API documentation
- User guides
- Tutorials
- Translation

## Community

Join our community to get help and discuss the project:

- [Discord Server](#) (coming soon)
- [GitHub Discussions](https://github.com/your-username/bandit-wargame-gui/discussions)
- [Twitter](#) (coming soon)

## License

By contributing, you agree that your contributions will be licensed under the [MIT License](LICENSE).

## Acknowledgments

- Thanks to all the contributors who have helped improve this project
- Special thanks to [OverTheWire](https://overthewire.org/) for the Bandit wargame
- Built with ❤️ by the open-source community
