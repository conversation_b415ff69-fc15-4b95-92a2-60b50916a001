"""
API Routing Fix Module

This module contains fixes for API routing issues, including health check endpoints.
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
from datetime import datetime
import os
import psutil

router = APIRouter()

def get_system_status() -> Dict[str, Any]:
    """Get system status information.
    
    Returns:
        Dict containing system status information
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "system": {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent
        },
        "process": {
            "pid": os.getpid(),
            "cpu_percent": psutil.Process().cpu_percent(),
            "memory_percent": psutil.Process().memory_percent()
        }
    }

@router.get("/health", response_model=Dict[str, Any])
async def health_check() -> Dict[str, Any]:
    """Health check endpoint.
    
    Returns:
        Dict containing health status information
    """
    return get_system_status()

@router.get("/api/health", response_model=Dict[str, Any])
async def api_health_check() -> Dict[str, Any]:
    """API health check endpoint.
    
    This is an alias for the /health endpoint to maintain backward compatibility.
    
    Returns:
        Dict containing health status information
    """
    return get_system_status()

def setup_routing(app):
    """Setup API routing for the application.
    
    Args:
        app: FastAPI application instance
    """
    # Include the router with both /health and /api/health endpoints
    app.include_router(router, tags=["health"])
    
    # Add exception handlers
    @app.exception_handler(404)
    async def not_found_exception_handler(request, exc):
        return JSONResponse(
            status_code=404,
            content={"message": "The requested resource was not found"},
        )
    
    @app.exception_handler(500)
    async def server_error_exception_handler(request, exc):
        return JSONResponse(
            status_code=500,
            content={"message": "Internal server error occurred"},
        )
