# Bandit Game - Testing Guide

This guide provides comprehensive instructions for testing the Bandit Game application, focusing on the health check endpoints and monitoring functionality.

## Table of Contents

1. [Health Check Endpoints](#health-check-endpoints)
2. [Testing System Health](#testing-system-health)
3. [Testing Database Connectivity](#testing-database-connectivity)
4. [Testing External Services](#testing-external-services)
5. [Automated Testing](#automated-testing)
6. [Load Testing](#load-testing)
7. [Security Testing](#security-testing)

## Health Check Endpoints

The application provides the following health check endpoints:

- `GET /health` - Basic health status
- `GET /api/health` - API health status (alias of /health)
- `GET /health/detailed` - Detailed health information (includes system metrics)

### Expected Responses

#### Basic Health Check
```http
GET /health

{
  "status": "healthy",
  "timestamp": "2023-01-01T12:00:00.000000"
}
```

#### Detailed Health Check
```http
GET /health/detailed

{
  "status": "healthy",
  "system": {
    "cpu_usage_percent": 24.5,
    "memory_usage_percent": 45.2,
    "memory_available_gb": 7.8,
    "disk_usage_percent": 32.1,
    "disk_free_gb": 456.7,
    "timestamp": "2023-01-01T12:00:00.000000"
  },
  "database": {
    "status": "healthy",
    "database": "connected",
    "timestamp": "2023-01-01T12:00:00.000000"
  },
  "external_services": {
    "auth_service": "healthy",
    "storage_service": "healthy"
  },
  "timestamp": "2023-01-01T12:00:00.000000"
}
```

## Testing System Health

### Prerequisites
- Python 3.8+
- `requests` library
- Running Bandit Game server

### Test Script

```python
import requests
import json

def test_health_endpoints():
    base_url = "http://localhost:8000"  # Update with your server URL
    
    # Test basic health endpoint
    response = requests.get(f"{base_url}/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert data["status"] in ["healthy", "degraded", "unhealthy"]
    
    # Test detailed health endpoint
    response = requests.get(f"{base_url}/health/detailed")
    assert response.status_code == 200
    data = response.json()
    
    # Verify response structure
    required_fields = ["status", "system", "database", "external_services"]
    for field in required_fields:
        assert field in data, f"Missing required field: {field}"
    
    print("All health checks passed!")

if __name__ == "__main__":
    test_health_endpoints()
```

## Testing Database Connectivity

1. **Connection Test**:
   - Stop the database service and verify that the health check reports the database as down
   - Restart the database and verify the health check reports it as healthy

2. **Query Performance**:
   - Monitor response times for database queries during peak load
   - Ensure the database connection pool is properly configured

## Testing External Services

1. **Service Availability**:
   - Test with external services offline to verify proper error handling
   - Verify that the health check correctly reports degraded status when external services are unavailable

2. **Timeout Handling**:
   - Test with artificially delayed responses from external services
   - Verify that timeouts are handled gracefully

## Automated Testing

### Unit Tests

Run the test suite with:

```bash
pytest tests/unit/
```

### Integration Tests

```bash
pytest tests/integration/
```

### End-to-End Tests

```bash
pytest tests/e2e/
```

## Load Testing

Use a tool like Locust to simulate multiple users accessing the health endpoints:

```python
from locust import HttpUser, task, between

class HealthCheckUser(HttpUser):
    wait_time = between(1, 3)
    
    @task
    def check_health(self):
        self.client.get("/health")
    
    @task(3)  # Higher weight for detailed checks
    def check_detailed_health(self):
        self.client.get("/health/detailed")
```

Run with:
```bash
locust -f locustfile.py
```

## Security Testing

1. **Authentication**:
   - Verify that sensitive health endpoints require authentication
   - Test with invalid credentials

2. **Rate Limiting**:
   - Verify that health endpoints are rate-limited to prevent abuse
   - Test with requests exceeding the rate limit

3. **Information Disclosure**:
   - Ensure that detailed health information is only accessible to authorized users
   - Verify that sensitive system information is properly redacted

## Monitoring and Alerting

Set up monitoring to alert on:
- Health check failures
- High system resource usage
- Database connection issues
- External service failures

## Troubleshooting

### Common Issues

1. **Health Check Failing**
   - Verify all required services are running
   - Check application logs for errors
   - Verify database connection settings

2. **Slow Response Times**
   - Check system resource usage
   - Review database query performance
   - Verify network latency between services

For additional assistance, please refer to the main documentation or contact the development team.
