import { useEffect, useRef, useState } from 'react';
import { Terminal as XTerm } from 'xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import 'xterm/css/xterm.css';

const Terminal = ({ socket, sessionId }) => {
  const terminalRef = useRef(null);
  const xtermRef = useRef(null);
  const fitAddonRef = useRef(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (!terminalRef.current) return;

    // Initialize xterm.js
    const xterm = new XTerm({
      cursorBlink: true,
      fontSize: 14,
      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
      theme: {
        background: '#1e293b', // Changed to navy blue (slate-800)
        foreground: '#ffd700', // Changed to gold
        cursor: '#ffd700', // Changed to gold
        selection: '#ffffff40',
      },
      cols: 80,
      rows: 24,
    });

    const fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();

    xterm.loadAddon(fitAddon);
    xterm.loadAddon(webLinksAddon);

    xterm.open(terminalRef.current);
    fitAddon.fit();

    xtermRef.current = xterm;
    fitAddonRef.current = fitAddon;

    // Handle terminal input
    xterm.onData((data) => {
      if (socket && isConnected) {
        socket.emit('ssh_command', {
          session_id: sessionId,
          command: data
        });
      }
    });

    // Handle window resize
    const handleResize = () => {
      if (fitAddonRef.current) {
        fitAddonRef.current.fit();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (xtermRef.current) {
        xtermRef.current.dispose();
      }
    };
  }, []);

  useEffect(() => {
    if (!socket || !xtermRef.current) return;

    const handleSSHOutput = (data) => {
      if (data.session_id === sessionId && xtermRef.current) {
        xtermRef.current.write(data.data);
      }
    };

    const handleSSHConnected = (data) => {
      if (data.session_id === sessionId) {
        setIsConnected(true);
        if (xtermRef.current) {
          xtermRef.current.write('\r\n\x1b[32mSSH connection established!\x1b[0m\r\n');
        }
      }
    };

    const handleSSHDisconnected = (data) => {
      if (data.session_id === sessionId) {
        setIsConnected(false);
        if (xtermRef.current) {
          xtermRef.current.write('\r\n\x1b[31mSSH connection closed!\x1b[0m\r\n');
        }
      }
    };

    const handleSSHError = (data) => {
      if (xtermRef.current) {
        xtermRef.current.write(`\r\n\x1b[31mError: ${data.message}\x1b[0m\r\n`);
      }
    };

    socket.on('ssh_output', handleSSHOutput);
    socket.on('ssh_connected', handleSSHConnected);
    socket.on('ssh_disconnected', handleSSHDisconnected);
    socket.on('ssh_error', handleSSHError);

    return () => {
      socket.off('ssh_output', handleSSHOutput);
      socket.off('ssh_connected', handleSSHConnected);
      socket.off('ssh_disconnected', handleSSHDisconnected);
      socket.off('ssh_error', handleSSHError);
    };
  }, [socket, sessionId]);

  const connectSSH = (username, password) => {
    if (socket) {
      socket.emit('ssh_connect', {
        hostname: 'bandit.labs.overthewire.org',
        port: 2220,
        username,
        password,
        session_id: sessionId
      });
    }
  };

  const disconnectSSH = () => {
    if (socket) {
      socket.emit('ssh_disconnect', {
        session_id: sessionId
      });
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between p-2 border-b border-green-800" style={{ backgroundColor: '#1e293b' }}>
        <h3 className="text-yellow-400 font-mono text-sm">Terminal</h3>
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
          <span className="text-xs text-gray-400">
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
      </div>
      <div 
        ref={terminalRef} 
        className="flex-1 bg-transparent"
        style={{ minHeight: '400px' }}
      />
    </div>
  );
};

export default Terminal;
