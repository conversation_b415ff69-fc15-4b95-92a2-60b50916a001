# Bandit Wargame GUI - Project Directory

This directory contains the source code for the Bandit Wargame GUI application. For complete documentation, please refer to the main project documentation.

## 📚 Project Documentation

- [Main README](../README.md) - Overview, features, and quick start guide
- [Deployment Guide](../DEPLOYMENT_GUIDE.md) - Setup and deployment instructions
- [API Reference](../API_REFERENCE.md) - Detailed API documentation
- [Configuration Guide](../CONFIGURATION.md) - Environment variables and settings
- [Contributing Guide](../CONTRIBUTING.md) - How to contribute to the project

## 🏗️ Project Structure

```
bandit-gui-app/
├── backend/                # Python Flask backend
│   └── bandit-backend/     # Backend source code
├── frontend/               # React frontend
│   └── bandit-frontend/    # Frontend source code
└── README.md               # This file
```

## 🚀 Quick Start

1. **Clone the repository** (if you haven't already):
   ```bash
   git clone https://github.com/yourusername/bandit-wargame-gui.git
   cd bandit-wargame-gui
   ```

2. **Set up the backend**:
   ```bash
   cd bandit-gui-app/backend/bandit-backend
   python -m venv venv
   # On Windows: .\venv\Scripts\activate
   source venv/bin/activate
   pip install -r requirements.txt
   ```

3. **Set up the frontend**:
   ```bash
   cd ../../frontend/bandit-frontend
   pnpm install
   ```

4. **Run in development mode**:
   - Backend: `python src/main.py` (from backend directory)
   - Frontend: `pnpm run dev` (from frontend directory)

## 🔧 Development

### Backend Development
- The backend is a Python Flask application
- Main entry point: `backend/bandit-backend/src/main.py`
- API routes are organized in the `routes/` directory
- Environment configuration: `.env` file in project root

### Frontend Development
- The frontend is a React application using Vite
- Main entry point: `frontend/bandit-frontend/src/main.jsx`
- Components are in the `components/` directory
- Uses Tailwind CSS for styling

## 📦 Building for Production

1. Build the frontend:
   ```bash
   cd frontend/bandit-frontend
   pnpm run build
   ```

2. The built files will be placed in `backend/bandit-backend/src/static/`

3. Run the production server:
   ```bash
   cd ../../backend/bandit-backend
   python src/main.py
   ```

## 🤝 Contributing

Please see the [Contributing Guide](../CONTRIBUTING.md) for details on how to contribute to this project.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.

## Support

For issues, questions, or contributions, please refer to the project documentation or create an issue in the repository.

