"""
Performance Testing Script for Bandit Wargame GUI

This module provides programmatic load testing using Locust's Environment and Runner APIs.
"""
import time
import asyncio
import statistics
import logging
from typing import Dict, List, Optional, Any, Callable
import pandas as pd
import matplotlib.pyplot as plt
from locust import HttpUser, task, between, events
from locust.env import Environment, Runner
from locust.log import setup_logging
from locust.stats import stats_printer, stats_history, StatsCSVFileWriter
from locust.runners import WorkerRunner

# Configuration
BASE_URL = "http://localhost:5000"
TEST_USERS = 100  # Number of concurrent users
TEST_DURATION = 300  # Test duration in seconds
RAMP_UP_TIME = 60  # Ramp-up time in seconds

# Test data
TEST_CREDENTIALS = {
    "hostname": "bandit.labs.overthewire.org",
    "port": 2220,
    "username": "bandit0",
    "password": "bandit0"
}

class PerformanceMetrics:
    """Class to collect and analyze performance metrics.
    
    Note: This class is kept for backward compatibility but most functionality
    has been moved to Locust's built-in statistics and reporting.
    """
    
    def __init__(self):
        self.metrics = {
            'response_times': [],
            'requests': [],
            'errors': [],
            'start_time': time.time(),
            'end_time': None
        }
    
    def add_metric(self, name: str, method: str, response_time: float, 
                  response_length: int, exception: Optional[Exception] = None):
        """Add a performance metric.
        
        This is now handled by Locust's built-in statistics collection.
        Kept for backward compatibility.
        """
        pass

    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the performance metrics"""
        if not self.metrics['response_times']:
            return {"error": "No metrics collected"}
        
        response_times = self.metrics['response_times']
        
        return {
            "total_requests": len(self.metrics['requests']),
            "total_errors": len(self.metrics['errors']),
            "error_rate": len(self.metrics['errors']) / len(self.metrics['requests']) if self.metrics['requests'] else 0,
            "response_time_avg": statistics.mean(response_times) if response_times else 0,
            "response_time_median": statistics.median(response_times) if response_times else 0,
            "response_time_95th": sorted(response_times)[int(len(response_times) * 0.95)] if response_times else 0,
            "response_time_max": max(response_times) if response_times else 0,
            "requests_per_second": len(self.metrics['requests']) / (time.time() - self.metrics['start_time'])
        }
    
    def generate_report(self, filename: str = "performance_report.html"):
        """Generate an HTML performance report"""
        summary = self.get_summary()
        
        # Create a DataFrame for requests data
        df = pd.DataFrame(self.metrics['requests'])
        
        # Generate time series data
        if not df.empty:
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            df.set_index('timestamp', inplace=True)
            
            # Resample to get requests per second
            rps = df.resample('1S').size()
            
            # Create a figure with subplots
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
            
            # Plot response times over time
            df['response_time'].plot(ax=ax1, alpha=0.5)
            ax1.set_title('Response Times Over Time')
            ax1.set_ylabel('Response Time (ms)')
            
            # Plot requests per second
            rps.plot(ax=ax2)
            ax2.set_title('Requests Per Second')
            ax2.set_ylabel('RPS')
            
            plt.tight_layout()
            
            # Save the plot
            plot_filename = filename.replace('.html', '.png')
            plt.savefig(plot_filename)
            plt.close()
        
        # Prepare variables for the report
        start_time = pd.to_datetime(self.metrics['start_time'], unit='s')
        duration = time.time() - self.metrics['start_time']
        total_requests = summary['total_requests']
        total_errors = summary['total_errors']
        error_rate = summary['error_rate']
        rps = summary['requests_per_second']
        avg_rt = summary['response_time_avg']
        median_rt = summary['response_time_median']
        p95_rt = summary['response_time_95th']
        max_rt = summary['response_time_max']
        
        # Generate HTML report using f-strings
        html = f"""
        <html>
        <head>
            <title>Performance Test Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .summary {{ background: #f5f5f5; padding: 15px; border-radius: 5px; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f2f2f2; }}
                .error {{ color: red; }}
                .plot {{ text-align: center; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <h1>Performance Test Report</h1>
            <div class="summary">
                <h2>Test Summary</h2>
                <p><strong>Start Time:</strong> {start_time}</p>
                <p><strong>Duration:</strong> {duration:.2f} seconds</p>
                <p><strong>Total Requests:</strong> {total_requests}</p>
                <p><strong>Total Errors:</strong> {total_errors}</p>
                <p><strong>Error Rate:</strong> {error_rate:.2%}</p>
                <p><strong>Requests Per Second:</strong> {rps:.2f}</p>
            </div>
            
            <h2>Response Times</h2>
            <table>
                <tr><th>Metric</th><th>Value (ms)</th></tr>
                <tr><td>Average</td><td>{avg_rt:.2f}</td></tr>
                <tr><td>Median</td><td>{median_rt:.2f}</td></tr>
                <tr><td>95th Percentile</td><td>{p95_rt:.2f}</td></tr>
                <tr><td>Maximum</td><td>{max_rt:.2f}</td></tr>
            </table>
            
            <div class="plot">
                <h3>Performance Metrics</h3>
                <img src="{plot_filename}" alt="Performance Metrics" style="max-width: 100%;">
            </div>
            
            <h2>Errors ({total_errors})</h2>
        """
        
        # Add errors to the report
        if self.metrics['errors']:
            html += """
            <table>
                <tr>
                    <th>Timestamp</th>
                    <th>Endpoint</th>
                    <th>Error</th>
                </tr>
            """
            
            for error in self.metrics['errors'][:50]:  # Limit to first 50 errors
                error_time = pd.to_datetime(error['timestamp'], unit='s')
                error_name = error['name']
                error_method = error['method']
                error_exception = error['exception']
                
                html += f"""
                <tr>
                    <td>{error_time}</td>
                    <td>{error_name} {error_method}</td>
                    <td class="error">{error_exception}</td>
                </tr>
                """
            
            html += "</table>"
            
            if len(self.metrics['errors']) > 50:
                html += f"<p>... and {len(self.metrics['errors']) - 50} more errors</p>"
        else:
            html += "<p>No errors occurred during testing.</p>"
        
        html += """
        </body>
        </html>
        """
        
        with open(filename, 'w') as f:
            f.write(html)
        
        print(f"Performance report generated: {filename}")
        return filename

class BanditUser(HttpUser):
    """Locust user class for load testing"""
    
    wait_time = between(1, 5)  # Random wait time between tasks
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.session_id = None
        self.level = 0
    
    def on_start(self):
        """Called when a user starts"""
        # Connect to SSH
        response = self.client.post(
            "/api/ssh/connect",
            json={
                "hostname": TEST_CREDENTIALS["hostname"],
                "port": TEST_CREDENTIALS["port"],
                "username": f"bandit{self.level}",
                "password": f"bandit{self.level}"
            },
            name="SSH Connect"
        )
        
        if response.status_code == 200:
            self.session_id = response.json().get("session_id")
    
    def on_stop(self):
        """Called when a user stops"""
        if self.session_id:
            self.client.post(
                "/api/ssh/disconnect",
                json={"session_id": self.session_id},
                name="SSH Disconnect"
            )
    
    @task(3)
    def execute_command(self):
        """Execute a command in the terminal"""
        if not self.session_id:
            return
        
        commands = ["ls", "pwd", "whoami", "id", "uname -a"]
        command = random.choice(commands)
        
        self.client.post(
            "/api/ssh/command",
            json={"session_id": self.session_id, "command": command},
            name="Execute Command"
        )
    
    @task(1)
    def get_level_info(self):
        """Get level information"""
        self.client.get(f"/api/levels/{self.level}", name="Get Level Info")
    
    @task(2)
    def chat_with_mentor(self):
        """Chat with the AI mentor"""
        messages = [
            "What should I do next?",
            "Can you give me a hint?",
            "How do I solve this level?",
            "What commands can I use?",
            "Help me with this level."
        ]
        
        self.client.post(
            "/api/mentor/chat",
            json={
                "session_id": f"test-session-{random.randint(1, 1000)}",
                "message": random.choice(messages)
            },
            name="Chat with Mentor"
        )

def setup_locust_environment() -> Environment:
    """Set up the Locust environment with custom event handlers."""
    # Setup logging
    setup_logging("INFO", None)
    
    # Create the environment
    env = Environment(user_classes=[BanditUser], events=events)
    
    # Set test configuration
    env.host = BASE_URL
    
    return env


def run_locust_test(env: Environment, num_users: int, spawn_rate: float, test_duration: int) -> None:
    """Run the Locust load test programmatically."""
    # Create a Runner instance
    runner = env.create_local_runner()
    
    # Start a greenlet that saves current stats to history
    stats_history.start(env.runner, 100)
    
    # Start a greenlet that saves current stats to CSV
    csv_writer = StatsCSVFileWriter(
        environment=env,
        base_filepath="locust_stats",
        full_history=True,
        percentiles_to_report=[50.0, 90.0, 95.0, 99.0, 99.9, 99.99, 100.0]
    )
    csv_writer.start()
    
    # Start the test
    print(f"Starting test with {num_users} users (spawn rate: {spawn_rate}/s)")
    runner.start(num_users, spawn_rate=spawn_rate)
    
    # Run for the specified duration
    print(f"Running test for {test_duration} seconds...")
    time.sleep(test_duration)
    
    # Stop the runner
    print("Test finished, stopping runners...")
    runner.quit()
    
    # Stop the CSV writer
    csv_writer.stop()
    
    # Generate final report
    generate_final_report(env)


def generate_final_report(env: Environment) -> None:
    """Generate a final performance report from the test results."""
    # Collect stats
    stats = []
    for name, value in env.stats.entries.items():
        if not name or name == '/':
            continue
        stats.append({
            'name': name,
            'method': value.method,
            'num_requests': value.num_requests,
            'num_failures': value.num_failures,
            'avg_response_time': value.avg_response_time,
            'min_response_time': value.min_response_time or 0,
            'max_response_time': value.max_response_time,
            'median_response_time': value.median_response_time,
            'ninetynine_response_time': value.get_response_time_percentile(0.99),
            'req_per_sec': value.total_rps
        })
    
    if not stats:
        print("\nNo statistics were collected during the test.")
        return
    
    # Create DataFrame and save to CSV
    df = pd.DataFrame(stats)
    df.to_csv('performance_metrics.csv', index=False)
    
    # Generate a simple plot
    try:
        plt.figure(figsize=(12, 6))
        df_sorted = df.sort_values('avg_response_time', ascending=False)
        df_sorted = df_sorted.head(10)  # Top 10 endpoints by response time
        
        plt.barh(df_sorted['name'] + ' (' + df_sorted['method'] + ')', 
                df_sorted['avg_response_time'])
        plt.title('Top 10 Endpoints by Average Response Time (ms)')
        plt.xlabel('Average Response Time (ms)')
        plt.tight_layout()
        plt.savefig('response_times.png')
        
    except Exception as e:
        print(f"\nWarning: Could not generate performance plot: {str(e)}")


def run_load_test():
    """Run the load test with the configured parameters."""
    print(f"Starting load test with {TEST_USERS} users for {TEST_DURATION} seconds...")
    
    # Setup environment
    env = setup_locust_environment()
    
    try:
        # Run the test
        run_locust_test(
            env=env,
            num_users=TEST_USERS,
            spawn_rate=min(10, TEST_USERS),  # Ramp up 10 users per second
            test_duration=TEST_DURATION
        )
        
        print("\nLoad test completed successfully!")
        print("\nPerformance report generated with the following files:")
        print("- performance_metrics.csv: Detailed metrics for all endpoints")
        print("- response_times.png: Visualization of top 10 slowest endpoints")
        print("- locust_stats_*.csv: Time-series data for all metrics")
        
    except KeyboardInterrupt:
        print("\nTest stopped by user")
    except Exception as e:
        print(f"\nError during test execution: {str(e)}")
        raise

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("load_test.log"),
            logging.StreamHandler()
        ]
    )
    
    # Run the load test
    run_load_test()
