# Launch script for Bandit Wargame GUI

# Function to check if a command exists
function Command-Exists {
    param ($command)
    $exists = $null -ne (Get-Command $command -ErrorAction SilentlyContinue)
    return $exists
}

# Check if Python is installed
if (-not (Command-Exists "python")) {
    Write-Host "Python is not installed or not in PATH. Please install Python 3.8 or higher." -ForegroundColor Red
    exit 1
}

# Check if Node.js is installed
if (-not (Command-Exists "node")) {
    Write-Host "Node.js is not installed or not in PATH. Please install Node.js 14 or higher." -ForegroundColor Red
    exit 1
}

# Check if pnpm is installed
if (-not (Command-Exists "pnpm")) {
    Write-Host "pnpm is not installed. Installing pnpm globally..." -ForegroundColor Yellow
    npm install -g pnpm
    if (-not $?) {
        Write-Host "Failed to install pnpm. Please install it manually with 'npm install -g pnpm'" -ForegroundColor Red
        exit 1
    }
}

# Function to start the backend server
function Start-Backend {
    $backendPath = "bandit-gui-app\backend\bandit-backend"
    
    # Check if virtual environment exists, if not create one
    $venvPath = "$backendPath\venv"  # Changed from .venv to venv for better compatibility
    
    # Remove existing venv if it exists to ensure clean state
    if (Test-Path $venvPath) {
        Remove-Item -Path $venvPath -Recurse -Force
    }
    
    Write-Host "Creating Python virtual environment..." -ForegroundColor Yellow
    python -m venv $venvPath
    if (-not $?) {
        Write-Host "Failed to create virtual environment." -ForegroundColor Red
        exit 1
    }
    
    # Activate virtual environment and install requirements
    Write-Host "Activating virtual environment and installing requirements..." -ForegroundColor Yellow
    $activateScript = "$venvPath\Scripts\Activate.ps1"
    if (-not (Test-Path $activateScript)) {
        Write-Host "Activation script not found at $activateScript" -ForegroundColor Red
        exit 1
    }
    
    # Install requirements using the full path to pip
    & "$venvPath\Scripts\pip.exe" install -r "$backendPath\requirements.txt"
    if (-not $?) {
        Write-Host "Failed to install Python requirements." -ForegroundColor Red
        exit 1
    }
    
    # Start the backend server in a new PowerShell window
    $backendScript = @"
    cd "$PWD\$backendPath\src"
    if (Test-Path "$PWD\..\venv\Scripts\python.exe") {
        & "$PWD\..\venv\Scripts\python.exe" main.py
    } else {
        Write-Host "Python executable not found in virtual environment"
        Write-Host "Tried path: $PWD\..\venv\Scripts\python.exe"
        pause
    }
"@
    
    $backendScript | Out-File -FilePath "$env:TEMP\start_backend.ps1" -Encoding utf8
    Start-Process powershell -ArgumentList "-NoExit", "-File", "$env:TEMP\start_backend.ps1" -WindowStyle Normal
    
    # Wait for backend to start
    Start-Sleep -Seconds 5
}

# Function to start the frontend
function Start-Frontend {
    $frontendPath = "bandit-gui-app\frontend\bandit-frontend"
    
    # Install frontend dependencies if node_modules doesn't exist
    if (-not (Test-Path "$frontendPath\node_modules")) {
        Write-Host "Installing frontend dependencies..." -ForegroundColor Yellow
        Set-Location $frontendPath
        pnpm install
        if (-not $?) {
            Write-Host "Failed to install frontend dependencies." -ForegroundColor Red
            exit 1
        }
        Set-Location "$PWD\..\..\.."
    }
    
    # Start the frontend in a new PowerShell window
    $frontendScript = @"
    cd "$PWD\$frontendPath"
    pnpm dev
"@
    
    $frontendScript | Out-File -FilePath "$env:TEMP\start_frontend.ps1" -Encoding utf8
    Start-Process powershell -ArgumentList "-NoExit", "-File", "$env:TEMP\start_frontend.ps1" -WindowStyle Normal
}

# Function to open browser
function Open-Browser {
    param ($url)
    
    try {
        Write-Host "Opening browser to $url..." -ForegroundColor Green
        Start-Process $url
        Write-Host "Browser opened successfully." -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to open browser automatically. Please manually navigate to $url" -ForegroundColor Yellow
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Main execution
Write-Host "Starting Bandit Wargame GUI..." -ForegroundColor Cyan

# Start backend
Write-Host "Starting backend server..." -ForegroundColor Green
Start-Backend

# Start frontend
Write-Host "Starting frontend development server..." -ForegroundColor Green
Start-Frontend

# Wait for frontend to be ready and open browser
Write-Host "Waiting for frontend to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 8
Open-Browser "http://localhost:5173"

Write-Host "`nApplication is starting up..." -ForegroundColor Cyan
Write-Host "- Backend: http://localhost:5001" -ForegroundColor Cyan
Write-Host "- Frontend: http://localhost:5173" -ForegroundColor Cyan
Write-Host "`nNote: The frontend might take a moment to start up." -ForegroundColor Yellow
