"""
Deployment Validation Tests for Bandit Wargame GUI
"""
import os
import sys
import json
import time
import subprocess
import requests
import socket
import platform
import shutil
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path

class DeploymentValidator:
    """Class to validate deployment of the Bandit Wargame GUI"""
    
    def __init__(self, base_url: str = "http://localhost:5001"):
        self.base_url = base_url.rstrip('/')
        self.results = {
            "checks": [],
            "summary": {
                "total_checks": 0,
                "passed": 0,
                "failed": 0,
                "warnings": 0
            }
        }
        
    def check_health_endpoint(self) -> None:
        """Check the basic health endpoint."""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            response.raise_for_status()
            data = response.json()
            
            self._add_check_result(
                "Health Check - Basic Endpoint",
                "PASSED",
                "Basic health endpoint is responding",
                {"status_code": response.status_code, "response": data}
            )
        except Exception as e:
            self._add_check_result(
                "Health Check - Basic Endpoint",
                "FAILED",
                f"Failed to access basic health endpoint: {str(e)}",
                {"error": str(e)}
            )
    
    def check_detailed_health_endpoint(self) -> None:
        """Check the detailed health endpoint."""
        try:
            response = requests.get(f"{self.base_url}/api/health", timeout=5)
            response.raise_for_status()
            data = response.json()
            
            required_fields = ['status', 'timestamp', 'system', 'services']
            if all(field in data for field in required_fields):
                self._add_check_result(
                    "Health Check - Detailed Endpoint",
                    "PASSED",
                    "Detailed health endpoint is responding with required fields",
                    {"status_code": response.status_code}
                )
            else:
                missing = [f for f in required_fields if f not in data]
                self._add_check_result(
                    "Health Check - Detailed Endpoint",
                    "FAILED",
                    f"Detailed health endpoint is missing required fields: {', '.join(missing)}",
                    {"missing_fields": missing}
                )
        except Exception as e:
            self._add_check_result(
                "Health Check - Detailed Endpoint",
                "FAILED",
                f"Failed to access detailed health endpoint: {str(e)}",
                {"error": str(e)}
            )
    
    def add_check(self, name: str, status: bool, message: str, details: str = ""):
        """Add a check result"""
        check = {
            "name": name,
            "status": "PASSED" if status else "FAILED",
            "message": message,
            "details": details,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self.results["checks"].append(check)
        self.results["summary"]["total_checks"] += 1
        
        if status:
            self.results["summary"]["passed"] += 1
        else:
            self.results["summary"]["failed"] += 1
    
    def add_warning(self, name: str, message: str, details: str = ""):
        """Add a warning"""
        warning = {
            "name": name,
            "status": "WARNING",
            "message": message,
            "details": details,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self.results["checks"].append(warning)
        self.results["summary"]["total_checks"] += 1
        self.results["summary"]["warnings"] += 1
    
    def check_environment_variables(self):
        """Check required environment variables"""
        required_vars = [
            "FLASK_APP",
            "FLASK_ENV",
            "SECRET_KEY",
            "DATABASE_URI",
            "OPENAI_API_KEY"
        ]
        
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            self.add_check(
                "Environment Variables",
                False,
                f"Missing required environment variables: {', '.join(missing_vars)}",
                "Set these variables in your environment or .env file"
            )
        else:
            self.add_check(
                "Environment Variables",
                True,
                "All required environment variables are set"
            )
    
    def check_directories(self):
        """Check required directories"""
        required_dirs = [
            "static",
            "templates",
            "logs",
            "data"
        ]
        
        missing_dirs = []
        
        for dir_name in required_dirs:
            dir_path = Path(dir_name)
            if not dir_path.exists() or not dir_path.is_dir():
                missing_dirs.append(dir_name)
        
        if missing_dirs:
            self.add_check(
                "Required Directories",
                False,
                f"Missing required directories: {', '.join(missing_dirs)}",
                "Create these directories in your project root"
            )
        else:
            self.add_check(
                "Required Directories",
                True,
                "All required directories exist"
            )
    
    def check_file_permissions(self):
        """Check file permissions"""
        sensitive_files = [
            ".env",
            "instance/config.py",
            "data/bandit_levels.json",
            "logs/app.log"
        ]
        
        insecure_files = []
        
        for file_path in sensitive_files:
            path = Path(file_path)
            if path.exists():
                # On Windows, we can't check file permissions the same way
                if platform.system() == 'Windows':
                    # Just check if the file exists on Windows
                    pass
                else:
                    # On Unix-like systems, check file permissions
                    mode = os.stat(path).st_mode
                    if mode & 0o077:  # Check if others have read/write/execute permissions
                        insecure_files.append(file_path)
        
        if insecure_files and platform.system() != 'Windows':
            self.add_warning(
                "File Permissions",
                f"Insecure permissions on sensitive files: {', '.join(insecure_files)}",
                "Restrict permissions using 'chmod 600' for sensitive files"
            )
        else:
            self.add_check(
                "File Permissions",
                True,
                "File permissions are secure"
            )
    
    def check_database_connection(self):
        """Check database connection"""
        try:
            # Try to import SQLAlchemy and check connection
            from sqlalchemy import create_engine
            from sqlalchemy.exc import SQLAlchemyError
            
            db_uri = os.getenv("DATABASE_URI")
            if not db_uri:
                self.add_check(
                    "Database Connection",
                    False,
                    "DATABASE_URI environment variable not set"
                )
                return
            
            engine = create_engine(db_uri)
            connection = engine.connect()
            connection.close()
            
            self.add_check(
                "Database Connection",
                True,
                "Successfully connected to the database"
            )
            
        except ImportError:
            self.add_warning(
                "Database Connection",
                "SQLAlchemy not installed, skipping database connection test",
                "Install SQLAlchemy with 'pip install sqlalchemy'"
            )
        except SQLAlchemyError as e:
            self.add_check(
                "Database Connection",
                False,
                f"Failed to connect to database: {str(e)}",
                "Check your DATABASE_URI and database server status"
            )
        except Exception as e:
            self.add_check(
                "Database Connection",
                False,
                f"Unexpected error during database connection test: {str(e)}"
            )
    
    def check_web_server(self):
        """Check if the web server is running and accessible"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            
            if response.status_code == 200:
                self.add_check(
                    "Web Server",
                    True,
                    "Web server is running and accessible"
                )
                
                # Check if the API is responding
                try:
                    response = requests.get(f"{self.base_url}/api/health", timeout=5)
                    if response.status_code == 200:
                        self.add_check(
                            "API Health",
                            True,
                            "API is responding to health checks"
                        )
                    else:
                        self.add_check(
                            "API Health",
                            False,
                            f"API health check returned status code {response.status_code}",
                            response.text
                        )
                except Exception as e:
                    self.add_check(
                        "API Health",
                        False,
                        f"Failed to check API health: {str(e)}"
                    )
            else:
                self.add_check(
                    "Web Server",
                    False,
                    f"Web server returned status code {response.status_code}",
                    response.text
                )
                
        except requests.exceptions.RequestException as e:
            self.add_check(
                "Web Server",
                False,
                f"Failed to connect to web server: {str(e)}",
                "Make sure the server is running and accessible"
            )
    
    def check_ssh_connectivity(self):
        """Check SSH connectivity to Bandit servers"""
        test_servers = [
            ("bandit.labs.overthewire.org", 2220),
            ("bandit.labs.overthewire.org", 2221),
            ("bandit.labs.overthewire.org", 2222)
        ]
        
        for host, port in test_servers:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((host, port))
                sock.close()
                
                if result == 0:
                    self.add_check(
                        f"SSH Connectivity - {host}:{port}",
                        True,
                        f"Successfully connected to {host}:{port}"
                    )
                else:
                    self.add_check(
                        f"SSH Connectivity - {host}:{port}",
                        False,
                        f"Failed to connect to {host}:{port}",
                        f"Connection error code: {result}"
                    )
                    
            except Exception as e:
                self.add_check(
                    f"SSH Connectivity - {host}:{port}",
                    False,
                    f"Error connecting to {host}:{port}",
                    str(e)
                )
    
    def check_docker_environment(self):
        """Check if running in a Docker container"""
        try:
            # Check for .dockerenv file or container env vars
            is_docker = (
                os.path.exists('/.dockerenv') or 
                os.environ.get('KUBERNETES_SERVICE_HOST') or
                os.environ.get('CONTAINER')
            )
            
            if is_docker:
                self.add_check(
                    "Docker Environment",
                    True,
                    "Running in a containerized environment"
                )
                
                # Check for memory limits
                try:
                    with open('/sys/fs/cgroup/memory/memory.limit_in_bytes', 'r') as f:
                        memory_limit = int(f.read().strip())
                    
                    if memory_limit < 1024 * 1024 * 1024:  # Less than 1GB
                        self.add_warning(
                            "Container Memory",
                            f"Container memory limit is low: {memory_limit / (1024*1024):.2f}MB",
                            "Consider increasing the container memory limit"
                        )
                    else:
                        self.add_check(
                            "Container Memory",
                            True,
                            f"Container memory limit: {memory_limit / (1024*1024):.2f}MB"
                        )
                except Exception as e:
                    self.add_warning(
                        "Container Memory",
                        "Could not determine container memory limit",
                        str(e)
                    )
                
            else:
                self.add_warning(
                    "Docker Environment",
                    "Not running in a containerized environment",
                    "Consider using Docker for consistent deployment"
                )
                
        except Exception as e:
            self.add_warning(
                "Docker Environment",
                "Error checking Docker environment",
                str(e)
            )
    
    def check_dependencies(self):
        """Check Python dependencies"""
        try:
            # Check Python version
            python_version = sys.version_info
            if python_version < (3, 8):
                self.add_warning(
                    "Python Version",
                    f"Python {python_version.major}.{python_version.minor} is not recommended",
                    "Python 3.8 or higher is recommended"
                )
            else:
                self.add_check(
                    "Python Version",
                    True,
                    f"Using Python {python_version.major}.{python_version.minor}.{python_version.micro}"
                )
            
            # Check pip packages
            try:
                import pkg_resources
                
                required_packages = [
                    'flask',
                    'flask-socketio',
                    'python-dotenv',
                    'paramiko',
                    'openai',
                    'requests',
                    'beautifulsoup4',
                    'sqlalchemy'
                ]
                
                missing_packages = []
                outdated_packages = []
                
                for package in required_packages:
                    try:
                        pkg_resources.get_distribution(package)
                    except pkg_resources.DistributionNotFound:
                        missing_packages.append(package)
                
                if missing_packages:
                    self.add_check(
                        "Python Dependencies",
                        False,
                        f"Missing required packages: {', '.join(missing_packages)}",
                        "Install with 'pip install -r requirements.txt'"
                    )
                else:
                    self.add_check(
                        "Python Dependencies",
                        True,
                        "All required Python packages are installed"
                    )
                    
            except ImportError:
                self.add_warning(
                    "Python Dependencies",
                    "Could not check Python packages (pkg_resources not available)",
                    "Install setuptools to verify package versions"
                )
                
        except Exception as e:
            self.add_warning(
                "Dependency Check",
                "Error checking dependencies",
                str(e)
            )
    
    def generate_report(self, output_file: str = "deployment_validation_report.json"):
        """Generate a JSON report of the validation results"""
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # Precompute values
        total = self.results["summary"]["total_checks"]
        passed = self.results["summary"]["passed"]
        warnings = self.results["summary"]["warnings"]
        failed = self.results["summary"]["failed"]
        abs_output_file = os.path.abspath(output_file)
        
        # Generate a simple text summary using f-strings
        summary = f"""
        Deployment Validation Report
        ===========================
        
        Summary:
        - Total Checks: {total}
        - Passed: {passed}
        - Warnings: {warnings}
        - Failed: {failed}
        
        Detailed results have been saved to: {abs_output_file}
        """
        
        print(summary)
        
        # Print failed checks
        failed_checks = [c for c in self.results["checks"] if c["status"] == "FAILED"]
        if failed_checks:
            print("\nFailed Checks:")
            for check in failed_checks:
                print(f"- {check['name']}: {check['message']}")
        
        # Print warnings
        warning_checks = [c for c in self.results["checks"] if c["status"] == "WARNING"]
        if warning_checks:
            print("\nWarnings:")
            for check in warning_checks:
                print(f"- {check['name']}: {check['message']}")
        
        return output_file
    
    def run_all_checks(self) -> None:
        """Run all deployment validation checks."""
        self.check_environment_variables()
        self.check_directories()
        self.check_services_running()
        self.check_api_endpoints()
        self.check_health_endpoint()
        self.check_detailed_health_endpoint()
        self.check_frontend_build()
        self.check_database_connection()
        self.check_security_headers()
        self.check_performance()
        self.check_logging_config()
    
    def run_all_checks(self):
        """Run all validation checks"""
        print("Starting deployment validation...\n")
        
        checks = [
            ("Environment Variables", self.check_environment_variables),
            ("Required Directories", self.check_directories),
            ("File Permissions", self.check_file_permissions),
            ("Docker Environment", self.check_docker_environment),
            ("Dependency Check", self.check_dependencies),
            ("Database Connection", self.check_database_connection),
            ("Web Server", self.check_web_server),
            ("SSH Connectivity", self.check_ssh_connectivity)
        ]
        
        for name, check in checks:
            print(f"Running check: {name}...")
            try:
                check()
            except Exception as e:
                self.add_check(
                    name,
                    False,
                    f"Error during check: {str(e)}",
                    "Check the application logs for more details"
                )
        
        print("\nValidation completed!")
        return self.generate_report()

def main():
    """Main function to run the deployment validation"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Run deployment validation tests for Bandit Wargame GUI')
    parser.add_argument('--url', type=str, default="http://localhost:5001",
                      help='Base URL of the application (default: http://localhost:5001)')
    parser.add_argument('--env', action='store_true',
                      help='Load base URL from BANDIT_API_URL environment variable if set')
    parser.add_argument('--output', type=str, default="deployment_validation_report.json",
                        help='Output file for the validation report (default: deployment_validation_report.json)')
    
    args = parser.parse_args()
    
    # Get base URL from environment if --env flag is set
    if args.env:
        base_url = os.environ.get('BANDIT_API_URL', args.url)
    else:
        base_url = args.url
    
    # Ensure URL has scheme
    if not base_url.startswith(('http://', 'https://')):
        base_url = f'http://{base_url}'
    
    validator = DeploymentValidator(base_url=base_url)
    report_file = validator.run_all_checks()
    
    # Exit with non-zero status if there are failures
    if validator.results["summary"]["failed"] > 0:
        sys.exit(1)
    else:
        sys.exit(0)

if __name__ == "__main__":
    main()
