graph TD

    8["User<br>External Actor"]
    subgraph 1["Frontend System<br>React"]
        22["Main Application Entry<br>React"]
        23["Core Application Component<br>React"]
        subgraph 2["Hooks &amp; Utilities<br>JavaScript"]
            29["Mobile Hook<br>React Hook"]
            30["Utility Functions<br>JavaScript"]
        end
        subgraph 3["UI Components<br>React"]
            24["AI Mentor Component<br>React"]
            25["Level Info Component<br>React"]
            26["SSH Login Component<br>React"]
            27["Terminal Component<br>React"]
            28["Shadcn UI Components<br>React"]
        end
        %% Edges at this level (grouped by source)
        3["UI Components<br>React"] -->|uses| 2["Hooks &amp; Utilities<br>JavaScript"]
        23["Core Application Component<br>React"] -->|uses| 3["UI Components<br>React"]
        22["Main Application Entry<br>React"] -->|renders| 23["Core Application Component<br>React"]
    end
    subgraph 4["Backend System<br>Python / Flask"]
        20["Bandit Wargame<br>External System"]
        21["External AI Service<br>External API"]
        9["Main Application<br>Python"]
        subgraph 5["Data Management<br>SQLite / JSON"]
            17["Application Database<br>SQLite"]
            18["Bandit Levels Data<br>JSON"]
            19["User Model<br>SQLAlchemy"]
            %% Edges at this level (grouped by source)
            19["User Model<br>SQLAlchemy"] -->|persists data in| 17["Application Database<br>SQLite"]
        end
        subgraph 6["Core Logic Modules<br>Python"]
            14["SSH Manager<br>Paramiko"]
            15["AI Mentor<br>OpenAI API"]
            16["Level Scraper<br>Python"]
        end
        subgraph 7["API Endpoints<br>Flask Routes"]
            10["User Routes<br>Python"]
            11["SSH Routes<br>Python"]
            12["Levels Routes<br>Python"]
            13["Mentor Routes<br>Python"]
        end
        %% Edges at this level (grouped by source)
        6["Core Logic Modules<br>Python"] -->|persists data in| 5["Data Management<br>SQLite / JSON"]
        7["API Endpoints<br>Flask Routes"] -->|accesses| 5["Data Management<br>SQLite / JSON"]
        7["API Endpoints<br>Flask Routes"] -->|orchestrates| 6["Core Logic Modules<br>Python"]
        9["Main Application<br>Python"] -->|dispatches to| 7["API Endpoints<br>Flask Routes"]
        16["Level Scraper<br>Python"] -->|populates| 18["Bandit Levels Data<br>JSON"]
        14["SSH Manager<br>Paramiko"] -->|connects to| 20["Bandit Wargame<br>External System"]
        15["AI Mentor<br>OpenAI API"] -->|integrates with| 21["External AI Service<br>External API"]
    end
    %% Edges at this level (grouped by source)
    8["User<br>External Actor"] -->|interacts with| 1["Frontend System<br>React"]
    1["Frontend System<br>React"] -->|makes API calls to| 4["Backend System<br>Python / Flask"]