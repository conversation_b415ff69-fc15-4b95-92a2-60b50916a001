# Bandit Wargame GUI

A web-based GUI application that simplifies playing the OverTheWire Bandit wargame, featuring an SSH terminal interface, level information display, and an AI mentor for interactive learning assistance.

![Application Screenshot](bandit-gui-app/frontend/bandit-frontend/public/ui_mockup.png)

## Features

### 🖥️ SSH Terminal Interface
- Real-time SSH connection to bandit.labs.overthewire.org:2220
- Interactive terminal using xterm.js with full terminal emulation
- WebSocket communication for responsive terminal interaction
- Connection status indicators and authentication management

### 📚 Level Information Display
- Automatic scraping of level data from OverTheWire website
- Display of level objectives, recommended commands, and learning materials
- Clean, organized presentation with proper styling
- Refresh functionality for updated data
- Direct links to external learning resources

### 🤖 AI Mentor System
- OpenAI GPT integration for intelligent guidance
- Context-aware responses based on current level and recent commands
- Hint system that provides guidance without revealing solutions
- Interactive chat interface with conversation history
- Educational approach that encourages learning and exploration

### 🎨 Modern User Interface
- Responsive design using React and Tailwind CSS
- Three-panel layout optimized for learning
- Dark theme suitable for terminal work
- Professional cybersecurity aesthetic
- Intuitive navigation and controls

## Technology Stack

### Backend (Python Flask)
- **Framework**: Flask with SocketIO
- **SSH Client**: Paramiko for secure connections
- **AI Integration**: OpenAI API (GPT-4o)
- **Web Scraping**: BeautifulSoup for level data extraction
- **Database**: SQLite for data persistence
- **Real-time Communication**: Flask-SocketIO for WebSockets
- **API Documentation**: Swagger/OpenAPI (planned)

### Frontend (React.js)
- **Framework**: React with modern hooks
- **Terminal**: xterm.js for terminal emulation
- **State Management**: React Context API
- **Styling**: Tailwind CSS with shadcn/ui components
- **Build Tool**: Vite for fast development and building
- **Package Manager**: pnpm

## Getting Started

### Prerequisites
- Python 3.11+
- Node.js 20+
- pnpm package manager (`npm install -g pnpm`)
- OpenAI API key (for AI mentor functionality)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/bandit-wargame-gui.git
   cd bandit-wargame-gui
   ```

2. **Set up the backend**
   ```bash
   cd bandit-gui-app/backend/bandit-backend
   python -m venv venv
   # On Windows: venv\Scripts\activate
   source venv/bin/activate
   pip install -r requirements.txt
   ```

3. **Set up the frontend**
   ```bash
   cd ../../frontend/bandit-frontend
   pnpm install
   ```

4. **Configure environment variables**
   Create a `.env` file in the repository root with the following content:
   ```env
   # LLM Configuration
   OPENAI_API_KEY=your_openai_api_key_here
   OPENAI_API_BASE=https://api.openai.com/v1
   OPENAI_API_MODEL=gpt-4o
   
   # Flask Configuration
   FLASK_APP=src/main.py
   FLASK_ENV=development
   SECRET_KEY=your-secret-key-here
   ```

## Running the Application

### Development Mode

1. **Start the backend server** (from `bandit-backend` directory):
   ```bash
   python src/main.py
   ```
   The backend will be available at `http://localhost:5001`

2. **Start the frontend development server** (from `bandit-frontend` directory):
   ```bash
   pnpm run dev --host
   ```
   The frontend will be available at `http://localhost:5173`

### Production Build

1. **Build the frontend** (from `bandit-frontend` directory):
   ```bash
   pnpm run build
   ```

2. **The Flask backend will serve the built frontend** when started normally.

## Project Structure

```
bandit-gui-app/
├── backend/
│   └── bandit-backend/
│       ├── src/
│       │   ├── main.py              # Flask application entry point
│       │   ├── ssh_manager.py       # SSH connection management
│       │   ├── ai_mentor.py         # AI mentor functionality
│       │   ├── level_scraper.py     # OverTheWire data scraping
│       │   ├── routes/              # API route blueprints
│       │   │   ├── ssh.py          # SSH WebSocket endpoints
│       │   │   ├── levels.py       # Level data API
│       │   │   ├── mentor.py       # AI mentor API
│       │   │   └── user.py         # User authentication
│       │   ├── models/             # Database models
│       │   └── static/             # Built frontend files
│       ├── requirements.txt        # Python dependencies
│       └── venv/                   # Virtual environment
├── frontend/
│   └── bandit-frontend/
│       ├── src/
│       │   ├── components/         # React components
│       │   │   ├── Terminal.jsx    # SSH terminal interface
│       │   │   ├── LevelInfo.jsx   # Level information display
│       │   │   ├── AIMentor.jsx    # AI mentor chat
│       │   │   └── SSHLogin.jsx    # SSH authentication
│       │   ├── App.jsx             # Main application component
│       │   └── main.jsx            # React entry point
│       ├── package.json            # Node.js dependencies
│       └── vite.config.js          # Vite configuration
└── docs/                           # Documentation files
```

## API Reference

### SSH Management
- `POST /api/ssh/connect` - Establish SSH connection
- `POST /api/ssh/command` - Execute command via SSH
- WebSocket events: `ssh_connect`, `ssh_command`, `ssh_output`

### Level Data
- `GET /api/levels/all` - Get all level data
- `GET /api/levels/<int:level_num>` - Get specific level
- `POST /api/levels/refresh` - Refresh scraped data
- `GET /api/levels/search` - Search levels
- `POST /api/levels/detect` - Detect current level from output

### AI Mentor
- `POST /api/mentor/chat` - Chat with AI mentor
- `GET /api/mentor/hint/<int:level_num>` - Get level hint
- `GET /api/mentor/explain/<command>` - Get command explanation
- WebSocket events: `mentor_message`, `mentor_response`

### User Authentication
- `POST /api/register` - Register new user
- `POST /api/login` - User login
- `POST /api/logout` - User logout

## Security Considerations

- SSH credentials are handled securely through the backend
- No direct SSH access from frontend
- Environment variables are used for sensitive configuration
- CORS is properly configured
- WebSocket connections are authenticated
- Rate limiting is implemented for API endpoints
- Input validation is performed on all user inputs

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is created for educational purposes. Please respect the OverTheWire terms of service when using this tool.

## Support

For issues, questions, or contributions, please refer to the project documentation or create an issue in the repository.

## Acknowledgements

- [OverTheWire](https://overthewire.org/) for providing the Bandit wargame
- [xterm.js](https://xtermjs.org/) for terminal emulation
- [OpenAI](https://openai.com/) for AI mentor capabilities
- [shadcn/ui](https://ui.shadcn.com/) for UI components
- [Flask](https://flask.palletsprojects.com/) and [React](https://reactjs.org/) communities
