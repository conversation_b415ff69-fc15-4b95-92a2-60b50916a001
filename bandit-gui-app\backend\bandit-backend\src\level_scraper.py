import requests
from bs4 import BeautifulSoup
import json
import re
from typing import Dict, List, Optional

class BanditLevelScraper:
    def __init__(self):
        self.base_url = "https://overthewire.org/wargames/bandit/"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def scrape_level(self, level_num: int) -> Optional[Dict]:
        """Scrape data for a specific level"""
        try:
            if level_num == 0:
                url = f"{self.base_url}bandit0.html"
            else:
                url = f"{self.base_url}bandit{level_num}.html"
            
            response = self.session.get(url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract level information
            level_data = {
                'level': level_num,
                'title': self._extract_title(soup),
                'goal': self._extract_goal(soup),
                'commands': self._extract_commands(soup),
                'reading_material': self._extract_reading_material(soup),
                'url': url
            }
            
            return level_data
            
        except Exception as e:
            print(f"Error scraping level {level_num}: {e}")
            return None
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """Extract level title"""
        title_elem = soup.find('h1')
        if title_elem:
            return title_elem.get_text().strip()
        return ""
    
    def _extract_goal(self, soup: BeautifulSoup) -> str:
        """Extract level goal description"""
        goal_section = soup.find('h2', string='Level Goal')
        if goal_section:
            goal_content = []
            for sibling in goal_section.find_next_siblings():
                if sibling.name == 'h2':
                    break
                if sibling.name == 'p':
                    goal_content.append(sibling.get_text().strip())
            return ' '.join(goal_content)
        return ""
    
    def _extract_commands(self, soup: BeautifulSoup) -> List[str]:
        """Extract recommended commands"""
        commands_section = soup.find('h2', string='Commands you may need to solve this level')
        commands = []
        
        if commands_section:
            # Look for the next paragraph or list after the commands heading
            next_elem = commands_section.find_next_sibling()
            while next_elem and next_elem.name != 'h2':
                if next_elem.name == 'p':
                    # Extract command links
                    links = next_elem.find_all('a')
                    for link in links:
                        command = link.get_text().strip()
                        if command and command not in commands:
                            commands.append(command)
                next_elem = next_elem.find_next_sibling()
        
        return commands
    
    def _extract_reading_material(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extract helpful reading material links"""
        reading_section = soup.find('h2', string='Helpful Reading Material')
        materials = []
        
        if reading_section:
            # Look for list items or paragraphs with links
            next_elem = reading_section.find_next_sibling()
            while next_elem and next_elem.name != 'h2':
                if next_elem.name in ['ul', 'ol']:
                    for li in next_elem.find_all('li'):
                        link = li.find('a')
                        if link:
                            materials.append({
                                'title': link.get_text().strip(),
                                'url': link.get('href', '')
                            })
                elif next_elem.name == 'p':
                    links = next_elem.find_all('a')
                    for link in links:
                        materials.append({
                            'title': link.get_text().strip(),
                            'url': link.get('href', '')
                        })
                next_elem = next_elem.find_next_sibling()
        
        return materials
    
    def scrape_all_levels(self, max_level: int = 30) -> Dict[int, Dict]:
        """Scrape data for all levels"""
        all_levels = {}
        
        # Scrape Level 0 (special case)
        level_0 = self.scrape_level(0)
        if level_0:
            all_levels[0] = level_0
        
        # Scrape levels 1 to max_level
        for level_num in range(1, max_level + 1):
            level_data = self.scrape_level(level_num)
            if level_data:
                all_levels[level_num] = level_data
            else:
                # If we can't scrape a level, we might have reached the end
                break
        
        return all_levels
    
    def save_to_file(self, data: Dict, filename: str):
        """Save scraped data to JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"Data saved to {filename}")
        except Exception as e:
            print(f"Error saving data: {e}")
    
    def load_from_file(self, filename: str) -> Optional[Dict]:
        """Load scraped data from JSON file"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading data: {e}")
            return None

def main():
    """Test the scraper"""
    scraper = BanditLevelScraper()
    
    # Test scraping a few levels
    print("Testing level scraper...")
    
    level_0 = scraper.scrape_level(0)
    print(f"Level 0: {level_0}")
    
    level_1 = scraper.scrape_level(1)
    print(f"Level 1: {level_1}")
    
    # Scrape all levels
    print("Scraping all levels...")
    all_levels = scraper.scrape_all_levels(5)  # Test with first 5 levels
    
    # Save to file
    scraper.save_to_file(all_levels, 'bandit_levels.json')

if __name__ == "__main__":
    main()

