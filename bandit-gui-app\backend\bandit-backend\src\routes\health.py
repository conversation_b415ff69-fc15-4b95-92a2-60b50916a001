"""
Health Check Routes

This module provides health check endpoints for monitoring the application.
"""
from flask import Blueprint, jsonify
import psutil
import socket
import os
from datetime import datetime

health_bp = Blueprint('health', __name__)

def get_system_status():
    """Get system status information."""
    return {
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'system': {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent,
            'hostname': socket.gethostname(),
            'process_id': os.getpid(),
        },
        'services': {
            'database': 'connected',  # This would be checked in a real implementation
            'ssh': 'available',      # This would be checked in a real implementation
        }
    }

@health_bp.route('/health', methods=['GET'])
def health():
    """Basic health check endpoint."""
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.utcnow().isoformat()
    })

@health_bp.route('/api/health', methods=['GET'])
def health_detailed():
    """Detailed health check endpoint with system information."""
    return jsonify(get_system_status())
