# Bandit Wargame GUI - Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Bandit Wargame GUI application in both development and production environments. The application consists of a Python Flask backend (port 5001) and a React frontend (port 5173 in development).

## System Requirements

### Development Environment
- **Operating System**: Linux, macOS, or Windows 10/11
- **Python**: 3.11 or higher
- **Node.js**: 20.x or higher
- **Package Managers**: 
  - pip (Python)
  - pnpm (Node.js) - Install with `npm install -g pnpm`
- **Memory**: Minimum 2GB RAM (4GB recommended)
- **Storage**: 500MB free space

### Production Environment
- **Web Server**: Nginx or Apache (recommended for production)
- **Process Manager**: Gunicorn with eventlet (for WebSocket support)
- **Database**: SQLite (included), PostgreSQL (recommended for production)

## Installation

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/bandit-wargame-gui.git
cd bandit-wargame-gui
```

### 2. Backend Setup

```bash
# Navigate to backend directory
cd bandit-gui-app/backend/bandit-backend

# Create and activate virtual environment
python -m venv venv
# On Windows:
# .\venv\Scripts\activate
# On Linux/macOS:
source venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt
```

### 3. Frontend Setup

```bash
# Navigate to frontend directory
cd ../../frontend/bandit-frontend

# Install Node.js dependencies
pnpm install
```

### 4. Environment Configuration

Create a `.env` file in the project root directory with the following variables:

```env
# Required for AI Mentor
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_MODEL=gpt-4o

# Optional: Database configuration (defaults to SQLite)
# DATABASE_URI=postgresql://user:password@localhost/bandit

# Optional: Secret key for session management
# SECRET_KEY=your-secret-key-here

# Optional: CORS configuration (comma-separated origins)
# ALLOWED_ORIGINS=http://localhost:5173,http://localhost:5001
```

## Running in Development Mode

### 1. Start Backend Server

```bash
cd bandit-gui-app/backend/bandit-backend
source venv/bin/activate  # On Windows: .\venv\Scripts\activate
python src/main.py
```

The backend will be available at `http://localhost:5001`

### 2. Start Frontend Development Server

In a new terminal:

```bash
cd bandit-gui-app/frontend/bandit-frontend
pnpm run dev --host
```

The frontend will be available at `http://localhost:5173`

## Production Deployment

### 1. Build Frontend for Production

```bash
cd bandit-gui-app/frontend/bandit-frontend
pnpm run build

# Copy built files to backend static directory
cp -r dist/* ../../backend/bandit-backend/src/static/
```

### 2. Configure Production Server

#### Option A: Using Gunicorn with Eventlet (Recommended)

1. Install Gunicorn and Eventlet:
   ```bash
   pip install gunicorn eventlet
   ```

2. Create a Gunicorn configuration file `gunicorn_config.py`:
   ```python
   bind = '0.0.0.0:5001'
   workers = 4
   worker_class = 'eventlet'
   timeout = 120
   keepalive = 5
   ```

3. Start the production server:
   ```bash
   cd bandit-gui-app/backend/bandit-backend
   gunicorn -c gunicorn_config.py "src.main:create_app()"
   ```

#### Option B: Using Nginx as Reverse Proxy

1. Install Nginx:
   ```bash
   # On Ubuntu/Debian
   sudo apt update
   sudo apt install nginx
   ```

2. Create a new Nginx configuration file at `/etc/nginx/sites-available/bandit-gui`:
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       # Frontend static files
       location / {
           proxy_pass http://localhost:5001;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }

       # Backend API
       location /api {
           proxy_pass http://localhost:5001;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

3. Enable the site and restart Nginx:
   ```bash
   sudo ln -s /etc/nginx/sites-available/bandit-gui /etc/nginx/sites-enabled/
   sudo nginx -t  # Test configuration
   sudo systemctl restart nginx
   ```

## Environment Variables Reference

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `OPENAI_API_KEY` | Yes | - | Your OpenAI API key |
| `OPENAI_API_BASE` | No | `https://api.openai.com/v1` | Base URL for OpenAI API |
| `OPENAI_API_MODEL` | No | `gpt-4o` | OpenAI model to use |
| `DATABASE_URI` | No | `sqlite:///app.db` | Database connection string |
| `SECRET_KEY` | No | Randomly generated | Secret key for session management |
| `FLASK_ENV` | No | `production` | Set to `development` for debug mode |
| `PORT` | No | `5001` | Port for the backend server |
| `ALLOWED_ORIGINS` | No | `*` | Comma-separated list of allowed origins |

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Find and kill the process using the port
   sudo lsof -i :5001  # For Linux/macOS
   netstat -ano | findstr :5001  # For Windows
   ```

2. **Python Import Errors**
   - Ensure virtual environment is activated
   - Reinstall requirements: `pip install -r requirements.txt`

3. **Node.js Dependencies**
   ```bash
   # Clear node_modules and reinstall
   rm -rf node_modules/
   pnpm install
   ```

4. **WebSocket Connection Issues**
   - Ensure your reverse proxy (if using) is configured for WebSockets
   - Check browser console for WebSocket connection errors

## Security Best Practices

1. **Environment Variables**
   - Never commit `.env` files to version control
   - Use environment-specific files (`.env.production`, `.env.development`)

2. **HTTPS**
   - Always use HTTPS in production
   - Obtain SSL certificates (e.g., Let's Encrypt)

3. **Database**
   - Use strong, unique passwords
   - Regular backups
   - Consider using PostgreSQL for production

4. **API Keys**
   - Rotate API keys regularly
   - Use the principle of least privilege
   - Monitor API usage

## Monitoring and Maintenance

### Logging
- Backend logs are written to `bandit-backend/logs/bandit.log`
- Nginx access/error logs (if using Nginx)

### Updates
1. Pull the latest changes from the repository
2. Update dependencies:
   ```bash
   # Backend
   pip install -r requirements.txt
   
   # Frontend
   pnpm install
   pnpm run build
   ```
3. Restart the application server

## Support

For additional help:
1. Check the [GitHub Issues](https://github.com/yourusername/bandit-wargame-gui/issues)
2. Review the application logs
3. Contact the development team

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
