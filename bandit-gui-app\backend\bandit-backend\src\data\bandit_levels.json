{"0": {"level": 0, "title": "", "goal": "The goal of this level is for you to log into the game using SSH.\nThe host to which you need to connect is\nbandit.labs.overthewire.org, on port 2220.\nThe username is bandit0 and the password is bandit0. Once\nlogged in, go to the Level 1 page to find out how to beat Level\n1.", "commands": ["ssh"], "reading_material": [{"title": "Secure Shell (SSH) on Wikipedia", "url": "https://en.wikipedia.org/wiki/Secure_Shell"}, {"title": "How to use SSH on wikiHow", "url": "https://www.wikihow.com/Use-SSH"}], "url": "https://overthewire.org/wargames/bandit/bandit0.html"}, "1": {"level": 1, "title": "", "goal": "The password for the next level is stored in a file called\nreadme located in the home directory. Use this password to log\ninto bandit1 using SSH. Whenever you find a password for a level,\nuse SSH (on port 2220) to log into that level and continue the game.", "commands": ["ls", "cd", "cat", "file", "du", "find"], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit1.html"}, "2": {"level": 2, "title": "", "goal": "The password for the next level is stored in a file called -\nlocated in the home directory", "commands": ["ls", "cd", "cat", "file", "du", "find"], "reading_material": [{"title": "Google Search for “dashed filename”", "url": "https://www.google.com/search?q=dashed+filename"}, {"title": "Advanced Bash-scripting Guide - Chapter 3 - Special Characters", "url": "https://linux.die.net/abs-guide/special-chars.html"}], "url": "https://overthewire.org/wargames/bandit/bandit2.html"}, "3": {"level": 3, "title": "", "goal": "The password for the next level is stored in a file called --spaces\nin this filename-- located in the home directory", "commands": ["ls", "cd", "cat", "file", "du", "find"], "reading_material": [{"title": "Google Search for “spaces in filename”", "url": "https://www.google.com/search?q=spaces+in+filename"}], "url": "https://overthewire.org/wargames/bandit/bandit3.html"}, "4": {"level": 4, "title": "", "goal": "The password for the next level is stored in a hidden file in the\ninhere directory.", "commands": ["ls", "cd", "cat", "file", "du", "find"], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit4.html"}, "5": {"level": 5, "title": "", "goal": "The password for the next level is stored in the only human-readable\nfile in the inhere directory. Tip: if your terminal is messed\nup, try the “reset” command.", "commands": ["ls", "cd", "cat", "file", "du", "find"], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit5.html"}, "6": {"level": 6, "title": "", "goal": "The password for the next level is stored in a file somewhere under\nthe inhere directory and has all of the following properties:", "commands": ["ls", "cd", "cat", "file", "du", "find"], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit6.html"}, "7": {"level": 7, "title": "", "goal": "The password for the next level is stored somewhere on the\nserver and has all of the following properties:", "commands": ["ls", "cd", "cat", "file", "du", "find", "grep"], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit7.html"}, "8": {"level": 8, "title": "", "goal": "The password for the next level is stored in the file data.txt\nnext to the word millionth", "commands": ["man"], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit8.html"}, "9": {"level": 9, "title": "", "goal": "The password for the next level is stored in the file data.txt\nand is the only line of text that occurs only once", "commands": [], "reading_material": [{"title": "Piping and Redirection", "url": "https://ryanstutorials.net/linuxtutorial/piping.php"}], "url": "https://overthewire.org/wargames/bandit/bandit9.html"}, "10": {"level": 10, "title": "", "goal": "The password for the next level is stored in the file data.txt\nin one of the few human-readable strings, preceded by several ‘=’\ncharacters.", "commands": [], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit10.html"}, "11": {"level": 11, "title": "", "goal": "The password for the next level is stored in the file data.txt,\nwhich contains base64 encoded data", "commands": [], "reading_material": [{"title": "Base64 on Wikipedia", "url": "https://en.wikipedia.org/wiki/Base64"}], "url": "https://overthewire.org/wargames/bandit/bandit11.html"}, "12": {"level": 12, "title": "", "goal": "The password for the next level is stored in the file data.txt,\nwhere all lowercase (a-z) and uppercase (A-Z) letters have been\nrotated by 13 positions", "commands": [], "reading_material": [{"title": "Rot13 on Wikipedia", "url": "https://en.wikipedia.org/wiki/ROT13"}], "url": "https://overthewire.org/wargames/bandit/bandit12.html"}, "13": {"level": 13, "title": "", "goal": "The password for the next level is stored in the file data.txt,\nwhich is a hexdump of a file that has been repeatedly compressed.\nFor this level it may be useful to create a directory under /tmp in\nwhich you can work. Use mkdir with a hard to guess directory name.\nOr better, use the command “mktemp -d”.\nThen copy the datafile using cp, and rename it using mv (read the\nmanpages!)", "commands": [], "reading_material": [{"title": "Hex dump on Wikipedia", "url": "https://en.wikipedia.org/wiki/Hex_dump"}], "url": "https://overthewire.org/wargames/bandit/bandit13.html"}, "14": {"level": 14, "title": "", "goal": "The password for the next level is stored in\n/etc/bandit_pass/bandit14 and can only be read by user\nbandit14. For this level, you don’t get the next password, but you\nget a private SSH key that can be used to log into the next level.\nNote: localhost is a hostname that refers to the machine\nyou are working on", "commands": [], "reading_material": [{"title": "SSH/OpenSSH/Keys", "url": "https://help.ubuntu.com/community/SSH/OpenSSH/Keys"}], "url": "https://overthewire.org/wargames/bandit/bandit14.html"}, "15": {"level": 15, "title": "", "goal": "The password for the next level can be retrieved by submitting the\npassword of the current level to port 30000 on localhost.", "commands": [], "reading_material": [{"title": "How the Internet works in 5 minutes (YouTube)", "url": "https://www.youtube.com/watch?v=7_LPdttKXPc"}, {"title": "IP Addresses", "url": "https://computer.howstuffworks.com/web-server5.htm"}, {"title": "IP Address on Wikipedia", "url": "https://en.wikipedia.org/wiki/IP_address"}, {"title": "Localhost on Wikipedia", "url": "https://en.wikipedia.org/wiki/Localhost"}, {"title": "Ports", "url": "https://computer.howstuffworks.com/web-server8.htm"}, {"title": "Port (computer networking) on Wikipedia", "url": "https://en.wikipedia.org/wiki/Port_(computer_networking)"}], "url": "https://overthewire.org/wargames/bandit/bandit15.html"}, "16": {"level": 16, "title": "", "goal": "The password for the next level can be retrieved by submitting the\npassword of the current level to port 30001 on localhost using\nSSL/TLS encryption. Helpful note: Getting “DONE”, “RENEGOTIATING” or “KEYUPDATE”? Read the\n“CONNECTED COMMANDS” section in the manpage.", "commands": [], "reading_material": [{"title": "Secure Socket Layer/Transport Layer Security on Wikipedia", "url": "https://en.wikipedia.org/wiki/Transport_Layer_Security"}, {"title": "OpenSSL Cookbook - Testing with OpenSSL", "url": "https://www.feistyduck.com/library/openssl-cookbook/online/testing-with-openssl/index.html"}], "url": "https://overthewire.org/wargames/bandit/bandit16.html"}, "17": {"level": 17, "title": "", "goal": "The credentials for the next level can be retrieved by submitting the\npassword of the current level to a port on localhost in the range\n31000 to 32000. First find out which of these ports have a server\nlistening on them. Then find out which of those speak SSL/TLS and which\ndon’t. There is only 1 server that will give the next credentials, the\nothers will simply send back to you whatever you send to it. Helpful note: Getting “DONE”, “RENEGOTIATING” or “KEYUPDATE”? Read the\n“CONNECTED COMMANDS” section in the manpage.", "commands": [], "reading_material": [{"title": "Port scanner on Wikipedia", "url": "https://en.wikipedia.org/wiki/Port_scanner"}], "url": "https://overthewire.org/wargames/bandit/bandit17.html"}, "18": {"level": 18, "title": "", "goal": "There are 2 files in the homedirectory: passwords.old and\npasswords.new. The password for the next level is in\npasswords.new and is the only line that has been changed between\npasswords.old and passwords.new NOTE: if you have solved this level and see ‘Byebye!’ when trying\nto log into bandit18, this is related to the next level, bandit19", "commands": [], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit18.html"}, "19": {"level": 19, "title": "", "goal": "The password for the next level is stored in a file readme in\nthe homedirectory. Unfortunately, someone has modified .bashrc\nto log you out when you log in with SSH.", "commands": [], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit19.html"}, "20": {"level": 20, "title": "", "goal": "To gain access to the next level, you should use the setuid binary\nin the homedirectory. Execute it without arguments to find out how\nto use it. The password for this level can be found in the usual\nplace (/etc/bandit_pass), after you have used the setuid binary.", "commands": [], "reading_material": [{"title": "setuid on Wikipedia", "url": "https://en.wikipedia.org/wiki/Setuid"}], "url": "https://overthewire.org/wargames/bandit/bandit20.html"}, "21": {"level": 21, "title": "", "goal": "There is a setuid binary in the homedirectory that does the\nfollowing: it makes a connection to localhost on the port you\nspecify as a commandline argument. It then reads a line of text from\nthe connection and compares it to the password in the previous level\n(bandit20). If the password is correct, it will transmit the\npassword for the next level (bandit21). NOTE: Try connecting to your own network daemon to see if it\nworks as you think", "commands": [], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit21.html"}, "22": {"level": 22, "title": "", "goal": "A program is running automatically at regular intervals from\ncron, the time-based job scheduler. Look in /etc/cron.d/ for\nthe configuration and see what command is being executed.", "commands": [], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit22.html"}, "23": {"level": 23, "title": "", "goal": "A program is running automatically at regular intervals from\ncron, the time-based job scheduler. Look in /etc/cron.d/ for\nthe configuration and see what command is being executed. NOTE: Looking at shell scripts written by other people is a\nvery useful skill. The script for this level is intentionally made\neasy to read. If you are having problems understanding what it does,\ntry executing it to see the debug information it prints.", "commands": [], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit23.html"}, "24": {"level": 24, "title": "", "goal": "A program is running automatically at regular intervals from\ncron, the time-based job scheduler. Look in /etc/cron.d/ for\nthe configuration and see what command is being executed. NOTE: This level requires you to create your own first\nshell-script. This is a very big step and you should be proud of\nyourself when you beat this level! NOTE 2: Keep in mind that your shell script is removed once\nexecuted, so you may want to keep a copy around…", "commands": [], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit24.html"}, "25": {"level": 25, "title": "", "goal": "A daemon is listening on port 30002 and will give you the password for\nbandit25 if given the password for bandit24 and a secret numeric 4-digit pincode.\nThere is no way to retrieve the pincode except by going through all of the 10000\ncombinations, called brute-forcing.\nYou do not need to create new connections each time", "commands": [], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit25.html"}, "26": {"level": 26, "title": "", "goal": "Logging in to bandit26 from bandit25 should be fairly easy…\nThe shell for user bandit26 is not /bin/bash, but something else.\nFind out what it is, how it works and how to break out of it.", "commands": [], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit26.html"}, "27": {"level": 27, "title": "", "goal": "Good job getting a shell! Now hurry and grab the password for bandit27!", "commands": [], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit27.html"}, "28": {"level": 28, "title": "", "goal": "There is a git repository at ssh://bandit27-git@localhost/home/<USER>/repo via the port 2220. The password for the user bandit27-git is the same as for the user bandit27. Clone the repository and find the password for the next level.", "commands": [], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit28.html"}, "29": {"level": 29, "title": "", "goal": "There is a git repository at ssh://bandit28-git@localhost/home/<USER>/repo via the port 2220. The password for the user bandit28-git is the same as for the user bandit28. Clone the repository and find the password for the next level.", "commands": [], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit29.html"}, "30": {"level": 30, "title": "", "goal": "There is a git repository at ssh://bandit29-git@localhost/home/<USER>/repo via the port 2220. The password for the user bandit29-git is the same as for the user bandit29. Clone the repository and find the password for the next level.", "commands": [], "reading_material": [], "url": "https://overthewire.org/wargames/bandit/bandit30.html"}}