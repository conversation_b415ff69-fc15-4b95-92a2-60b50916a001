"""
Tests for the AI Mentor module
"""
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from openai import OpenAIError, APIError
from src.ai_mentor import AIMentor, MentorManager

# Sample test data
class MockChoice:
    def __init__(self, content):
        self.message = MockMessage(content)

class MockMessage:
    def __init__(self, content):
        self.content = content

class MockResponse:
    def __init__(self, content):
        self.choices = [MockChoice(content)]

SAMPLE_CHAT_RESPONSE = MockResponse("This is a test response.")
SAMPLE_HINT_RESPONSE = MockResponse("Try using the 'ls' command to list files.")
SAMPLE_EXPLANATION_RESPONSE = MockResponse("The 'ls' command lists directory contents.")

@pytest.fixture
def mock_openai_client():
    with patch('src.ai_mentor.OpenAI') as mock_client:
        mock_client.return_value.chat.completions.create = AsyncMock(return_value=SAMPLE_CHAT_RESPONSE)
        yield mock_client.return_value.chat.completions

@pytest.fixture
def ai_mentor():
    return AIMentor(api_key="test-api-key")

@pytest.fixture
def mentor_manager():
    return MentorManager(api_key="test-api-key")

@pytest.mark.asyncio
async def test_chat_success(ai_mentor, mock_openai_client):
    """Test successful chat interaction"""
    mock_openai_client.create = AsyncMock(return_value=SAMPLE_CHAT_RESPONSE)
    
    response = await ai_mentor.chat("Hello, AI")
    
    assert response == "This is a test response."
    mock_openai_client.create.assert_awaited_once()

@pytest.mark.asyncio
async def test_chat_with_history(ai_mentor, mock_openai_client):
    """Test chat with conversation history"""
    mock_openai_client.create = AsyncMock(return_value=SAMPLE_CHAT_RESPONSE)
    
    # First message
    await ai_mentor.chat("Hello, AI")
    
    # Second message with history
    await ai_mentor.chat("What was my previous message?")
    
    # Check that history was maintained
    assert len(ai_mentor.conversation_history) == 4  # 2 user + 2 assistant messages
    assert "Hello, AI" in ai_mentor.conversation_history[0]["content"]
    assert "What was my previous message?" in ai_mentor.conversation_history[2]["content"]

@pytest.mark.asyncio
async def test_chat_error_handling(ai_mentor, mock_openai_client):
    """Test error handling during chat"""
    mock_openai_client.create.side_effect = APIError("API Error")
    
    with pytest.raises(Exception) as exc_info:
        await ai_mentor.chat("Test message")
    
    assert "API Error" in str(exc_info.value)

@pytest.mark.asyncio
async def test_get_hint_success(ai_mentor, mock_openai_client):
    """Test getting a hint for a level"""
    mock_openai_client.create = AsyncMock(return_value=SAMPLE_HINT_RESPONSE)
    
    hint = await ai_mentor.get_hint(0)
    
    assert "Try using the 'ls' command" in hint
    mock_openai_client.create.assert_awaited_once()

@pytest.mark.asyncio
async def test_explain_command_success(ai_mentor, mock_openai_client):
    """Test explaining a command"""
    mock_openai_client.create = AsyncMock(return_value=SAMPLE_EXPLANATION_RESPONSE)
    
    explanation = await ai_mentor.explain_command("ls -la")
    
    assert "lists directory contents" in explanation
    mock_openai_client.create.assert_awaited_once()

def test_reset_conversation(ai_mentor):
    """Test resetting the conversation history"""
    # Add some history
    ai_mentor.conversation_history = [
        {"role": "user", "content": "Hello"},
        {"role": "assistant", "content": "Hi there!"}
    ]
    
    ai_mentor.reset_conversation()
    
    assert len(ai_mentor.conversation_history) == 0

@pytest.mark.asyncio
async def test_mentor_manager_get_mentor(mentor_manager):
    """Test getting a mentor instance"""
    mentor1 = mentor_manager.get_mentor("session1")
    mentor2 = mentor_manager.get_mentor("session2")
    
    assert mentor1 is not None
    assert mentor2 is not None
    assert mentor1 != mentor2  # Different sessions get different mentors
    
    # Same session ID returns same mentor
    assert mentor_manager.get_mentor("session1") == mentor1

@pytest.mark.asyncio
async def test_mentor_manager_cleanup(mentor_manager):
    """Test cleanup of inactive mentors"""
    # Create a mentor and make it inactive
    mentor = mentor_manager.get_mentor("inactive-session")
    mentor.last_activity = 0  # Set to epoch to simulate inactivity
    
    # Run cleanup
    mentor_manager.cleanup_inactive_mentors()
    
    # Should be removed from active mentors
    assert "inactive-session" not in mentor_manager.mentors

@pytest.mark.asyncio
async def test_mentor_manager_chat(mentor_manager, mock_openai_client):
    """Test chat through mentor manager"""
    mock_openai_client.return_value = SAMPLE_CHAT_RESPONSE
    
    response = await mentor_manager.chat("session1", "Hello, AI")
    
    assert response == "This is a test response."
    assert "session1" in mentor_manager.mentors

@pytest.mark.asyncio
async def test_mentor_manager_get_hint(mentor_manager, mock_openai_client):
    """Test getting hint through mentor manager"""
    mock_openai_client.return_value = SAMPLE_HINT_RESPONSE
    
    hint = await mentor_manager.get_hint("session1", 0)
    
    assert "Try using the 'ls' command" in hint

@pytest.mark.asyncio
async def test_mentor_manager_explain_command(mentor_manager, mock_openai_client):
    """Test explaining command through mentor manager"""
    mock_openai_client.return_value = SAMPLE_EXPLANATION_RESPONSE
    
    explanation = await mentor_manager.explain_command("session1", "ls -la")
    
    assert "lists directory contents" in explanation

def test_mentor_manager_reset_conversation(mentor_manager):
    """Test resetting conversation through mentor manager"""
    mentor = mentor_manager.get_mentor("session1")
    mentor.conversation_history = [{"role": "user", "content": "Test"}]
    
    mentor_manager.reset_conversation("session1")
    
    assert len(mentor.conversation_history) == 0

@pytest.mark.asyncio
async def test_mentor_manager_invalid_session(mentor_manager):
    """Test operations on non-existent session"""
    with pytest.raises(ValueError):
        await mentor_manager.chat("nonexistent", "Hello")
    
    with pytest.raises(ValueError):
        await mentor_manager.get_hint("nonexistent", 0)
    
    with pytest.raises(ValueError):
        await mentor_manager.explain_command("nonexistent", "ls")
    
    with pytest.raises(ValueError):
        mentor_manager.reset_conversation("nonexistent")

@pytest.mark.asyncio
async def test_mentor_manager_concurrent_access(mentor_manager, mock_openai_client):
    """Test concurrent access to mentor manager"""
    import asyncio
    
    async def chat_task(session_id, message):
        return await mentor_manager.chat(session_id, message)
    
    # Mock the OpenAI response
    mock_openai_client.return_value = SAMPLE_CHAT_RESPONSE
    
    # Create multiple tasks
    tasks = [
        chat_task(f"session-{i}", f"Message {i}") 
        for i in range(5)
    ]
    
    # Run concurrently
    responses = await asyncio.gather(*tasks)
    
    # Verify all responses were successful
    assert all(response == "This is a test response." for response in responses)
    
    # Verify all sessions were created
    for i in range(5):
        assert f"session-{i}" in mentor_manager.mentors

@pytest.mark.asyncio
async def test_mentor_manager_rate_limiting(mentor_manager, mock_openai_client):
    """Test rate limiting in mentor manager"""
    # Set a low rate limit for testing
    mentor_manager.RATE_LIMIT = 2  # 2 requests per second
    
    # Mock the OpenAI response
    mock_openai_client.return_value = SAMPLE_CHAT_RESPONSE
    
    # First two requests should pass
    await mentor_manager.chat("session1", "Message 1")
    await mentor_manager.chat("session1", "Message 2")
    
    # Third request should be rate limited
    with pytest.raises(Exception) as exc_info:
        await mentor_manager.chat("session1", "Message 3")
    
    assert "Rate limit exceeded" in str(exc_info.value)
