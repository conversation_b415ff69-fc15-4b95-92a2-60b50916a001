import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const SSHLogin = ({ onConnect, isConnected }) => {
  const [username, setUsername] = useState('bandit0');
  const [password, setPassword] = useState('bandit0');
  const [isConnecting, setIsConnecting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!username || !password) return;

    setIsConnecting(true);
    try {
      await onConnect(username, password);
    } finally {
      setIsConnecting(false);
    }
  };

  if (isConnected) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-green-600">Connected</CardTitle>
          <CardDescription>
            SSH connection established to bandit.labs.overthewire.org
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-600">
              Logged in as {username}
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>SSH Connection</CardTitle>
        <CardDescription>
          Connect to bandit.labs.overthewire.org:2220
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="bandit0"
              disabled={isConnecting}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="bandit0"
              disabled={isConnecting}
            />
          </div>
          <Button 
            type="submit" 
            className="w-full"
            disabled={isConnecting || !username || !password}
          >
            {isConnecting ? 'Connecting...' : 'Connect'}
          </Button>
        </form>
        <div className="mt-4 text-xs text-gray-500">
          <p>Default credentials for Level 0:</p>
          <p>Username: bandit0</p>
          <p>Password: bandit0</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default SSHLogin;

