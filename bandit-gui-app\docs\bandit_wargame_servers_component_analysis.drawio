<?xml version="1.0" encoding="UTF-8"?>
      <mxfile host="codeviz.app" modified="2025-08-25T14:06:31.120Z" agent="CodeViz Exporter" version="14.6.5" type="device">
        <diagram id="codeviz-diagram" name="System Diagram">
          <mxGraphModel dx="1000" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
              <mxCell id="0"/>
              <mxCell id="1" parent="0"/>
              <mxCell id="aiMentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details-wrapper">
                <mxGeometry x="220" y="560" width="840" height="540" as="geometry"/>
              </mxCell>
              <mxCell id="aiMentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="ai_mentor.py Implementation" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details-wrapper">
                <mxGeometry x="228" y="568" width="764" height="24" as="geometry"/>
              </mxCell>
<mxCell id="mentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details-wrapper">
                <mxGeometry x="50" y="90" width="990" height="400" as="geometry"/>
              </mxCell>
              <mxCell id="mentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="mentor.py Implementation" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details-wrapper">
                <mxGeometry x="58" y="98" width="914" height="24" as="geometry"/>
              </mxCell>
<mxCell id="APIRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper">
                <mxGeometry x="130" y="255" width="690" height="220" as="geometry"/>
              </mxCell>
              <mxCell id="APIRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown_label" value="API Routes&lt;br&gt;Flask Blueprints" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper">
                <mxGeometry x="138" y="263" width="614" height="24" as="geometry"/>
              </mxCell>
<mxCell id="CoreLogic_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper">
                <mxGeometry x="300" y="545" width="540" height="220" as="geometry"/>
              </mxCell>
              <mxCell id="CoreLogic_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown_label" value="Core Logic Modules&lt;br&gt;Python Classes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper">
                <mxGeometry x="308" y="553" width="464" height="24" as="geometry"/>
              </mxCell>
<mxCell id="DatabaseModels_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper">
                <mxGeometry x="50" y="545" width="260" height="380" as="geometry"/>
              </mxCell>
              <mxCell id="DatabaseModels_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown_label" value="Database and Models&lt;br&gt;SQLAlchemy" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper">
                <mxGeometry x="58" y="553" width="184" height="24" as="geometry"/>
              </mxCell>
<mxCell id="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                <mxGeometry x="50" y="470" width="390" height="540" as="geometry"/>
              </mxCell>
              <mxCell id="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper_label" value="High-Level Architecture" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                <mxGeometry x="58" y="478" width="314" height="24" as="geometry"/>
              </mxCell>
<mxCell id="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                <mxGeometry x="430" y="440" width="724" height="700" as="geometry"/>
              </mxCell>
              <mxCell id="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper_label" value="Core Application Component: [App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                <mxGeometry x="438" y="448" width="648" height="24" as="geometry"/>
              </mxCell>
<mxCell id="section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                <mxGeometry x="1130" y="460" width="260" height="700" as="geometry"/>
              </mxCell>
              <mxCell id="section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture-wrapper_label" value="High-Level Architecture" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                <mxGeometry x="1138" y="468" width="184" height="24" as="geometry"/>
              </mxCell>
<mxCell id="section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                <mxGeometry x="1394" y="450" width="410" height="700" as="geometry"/>
              </mxCell>
              <mxCell id="section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper_label" value="Mid-Level Component Interaction" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                <mxGeometry x="1402" y="458" width="334" height="24" as="geometry"/>
              </mxCell>
<mxCell id="section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                <mxGeometry x="60" y="480" width="1070" height="1110" as="geometry"/>
              </mxCell>
              <mxCell id="section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details-wrapper_label" value="Low-Level Implementation Details" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                <mxGeometry x="68" y="488" width="994" height="24" as="geometry"/>
              </mxCell>
<mxCell id="section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                <mxGeometry x="1794" y="440" width="410" height="700" as="geometry"/>
              </mxCell>
              <mxCell id="section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper_label" value="Conclusion" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                <mxGeometry x="1802" y="448" width="334" height="24" as="geometry"/>
              </mxCell>
<mxCell id="section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="search-results-group-d13861cdf47babc131626e83fcdf70ab">
                <mxGeometry x="616" y="450" width="540" height="540" as="geometry"/>
              </mxCell>
              <mxCell id="section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture-wrapper_label" value="High-Level Architecture" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="search-results-group-d13861cdf47babc131626e83fcdf70ab">
                <mxGeometry x="624" y="458" width="464" height="24" as="geometry"/>
              </mxCell>
<mxCell id="section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="search-results-group-d13861cdf47babc131626e83fcdf70ab">
                <mxGeometry x="1156" y="440" width="850" height="945" as="geometry"/>
              </mxCell>
              <mxCell id="section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper_label" value="Mid-Level Component Breakdown" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="search-results-group-d13861cdf47babc131626e83fcdf70ab">
                <mxGeometry x="1164" y="448" width="774" height="24" as="geometry"/>
              </mxCell>
<mxCell id="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="search-results-group-d13861cdf47babc131626e83fcdf70ab">
                <mxGeometry x="50" y="470" width="576" height="890" as="geometry"/>
              </mxCell>
              <mxCell id="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper_label" value="Implementation Details" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="search-results-group-d13861cdf47babc131626e83fcdf70ab">
                <mxGeometry x="58" y="478" width="500" height="24" as="geometry"/>
              </mxCell>
              <mxCell id="search-results-group-d13861cdf47babc131626e83fcdf70ab" value="Search Results" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="12" y="217" width="2006" height="1385" as="geometry"/>
                  </mxCell>
<mxCell id="search-results-group-78d119381d84ebd0cea743684a30dac2" value="Search Results" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="2058" y="12" width="2208" height="1590" as="geometry"/>
                  </mxCell>
<mxCell id="search-results-group-69983580b1be33b446c4ee837bdf434c" value="Search Results" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="4306" y="472" width="1144" height="1130" as="geometry"/>
                  </mxCell>
<mxCell id="terminal-69983580b1be33b446c4ee837bdf434c" value="Terminal" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="4818" y="1802" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="terminal-78d119381d84ebd0cea743684a30dac2" value="Terminal" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="3102" y="1802" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="terminal-d13861cdf47babc131626e83fcdf70ab" value="Terminal" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="955" y="1802" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="search-result-69983580b1be33b446c4ee837bdf434c" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                    <mxGeometry x="175" y="80" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="search-result-78d119381d84ebd0cea743684a30dac2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                    <mxGeometry x="541" y="80" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="search-result-d13861cdf47babc131626e83fcdf70ab" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-d13861cdf47babc131626e83fcdf70ab">
                    <mxGeometry x="278" y="80" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="aiMentor_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" value="AI Mentor Capabilities&lt;br&gt;Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper">
                    <mxGeometry x="40" y="440" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" value="Backend Application&lt;br&gt;Node.js/Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper">
                    <mxGeometry x="60" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="banditServers_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" value="Bandit Wargame Servers&lt;br&gt;SSH Target" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper">
                    <mxGeometry x="190" y="410" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="frontendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" value="Frontend Application&lt;br&gt;React" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper">
                    <mxGeometry x="60" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="aiMentorComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="AIMentor Component&lt;br&gt;React Component" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="524" y="440" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="App Component&lt;br&gt;React Functional Component" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="484" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="backendServer_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="Backend Server&lt;br&gt;Node.js/Socket.IO" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="354" y="600" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="levelInfo_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="LevelInfo Component&lt;br&gt;React Component" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="40" y="410" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="sshLogin_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="SSHLogin Component&lt;br&gt;React Component" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="484" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="terminalComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="Terminal Component&lt;br&gt;xterm.js Wrapper" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="190" y="420" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="webSocketClient_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="WebSocket Client&lt;br&gt;socket.io-client" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="374" y="440" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="xtermJs_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="xterm.js Library&lt;br&gt;Terminal Emulator" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="190" y="580" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture" value="AI Mentor Module&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture-wrapper">
                    <mxGeometry x="60" y="600" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="backend_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture" value="Backend&lt;br&gt;Python Application" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture-wrapper">
                    <mxGeometry x="60" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="frontend_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture" value="Frontend&lt;br&gt;Application" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture-wrapper">
                    <mxGeometry x="60" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture" value="Mentor Routes Module&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture-wrapper">
                    <mxGeometry x="40" y="440" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" value="ai_mentor Module&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper">
                    <mxGeometry x="60" y="440" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="externalAILib_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" value="External AI Library&lt;br&gt;OpenAI Client" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper">
                    <mxGeometry x="40" y="600" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="frontendApp_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" value="Frontend Application&lt;br&gt;React" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper">
                    <mxGeometry x="80" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="httpLibs_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" value="HTTP Libraries&lt;br&gt;httpx/requests" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper">
                    <mxGeometry x="190" y="570" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" value="mentor Routes Module&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper">
                    <mxGeometry x="80" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="webFramework_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" value="Web Framework&lt;br&gt;Flask" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper">
                    <mxGeometry x="210" y="410" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="BanditAIMentor&lt;br&gt;Class" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="aiMentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="391" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="chatRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="/chat&lt;br&gt;POST Route" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="mentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="640" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="clearConversationMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="clear_conversation()&lt;br&gt;Method" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="aiMentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="40" y="410" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="clearRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="/clear/&lt;br&gt;POST Route" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="mentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="40" y="250" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="explainCommandMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="explain_command()&lt;br&gt;Method" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="aiMentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="190" y="420" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="explainRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="/explain/&lt;br&gt;GET Route" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="mentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="340" y="270" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="getLevelHintMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="get_level_hint()&lt;br&gt;Method" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="aiMentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="340" y="440" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="getResponseMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="get_response()&lt;br&gt;Method" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="aiMentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="640" y="380" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="globalInstance_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="ai_mentor&lt;br&gt;Global Instance" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="aiMentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="391" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="hintRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="/hint/&lt;br&gt;GET Route" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="mentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="490" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="initMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="__init__()&lt;br&gt;Method" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="aiMentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="490" y="420" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="initSocketIOFunc_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="init_mentor_socketio()&lt;br&gt;Function" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="mentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="770" y="140" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="mentorBlueprint_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="mentor_bp&lt;br&gt;Flask Blueprint" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="mentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="604" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="socketIOClear_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="@socketio.on(&apos;mentor_clear&apos;)&lt;br&gt;Event Handler" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="mentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="190" y="270" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="socketIOMessage_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" value="@socketio.on(&apos;mentor_message&apos;)&lt;br&gt;Event Handler" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="mentorPy_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                    <mxGeometry x="790" y="300" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="banditAIMentor_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" value="BanditAIMentor&lt;br&gt;Core AI Logic" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper">
                    <mxGeometry x="70" y="440" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="frontendApp_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" value="Frontend Application&lt;br&gt;User Interface" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper">
                    <mxGeometry x="40" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="mentorRoutes_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" value="Mentor Routes&lt;br&gt;API Endpoints" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper">
                    <mxGeometry x="210" y="140" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="openAIApi_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" value="OpenAI API&lt;br&gt;External Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper">
                    <mxGeometry x="70" y="600" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="restApi_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" value="REST API&lt;br&gt;HTTP Communication" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper">
                    <mxGeometry x="40" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="socketIO_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" value="SocketIO&lt;br&gt;Real-time Communication" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper">
                    <mxGeometry x="190" y="300" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture" value="AI Mentor&lt;br&gt;Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture-wrapper">
                    <mxGeometry x="190" y="420" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="banditBackend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture" value="Bandit Backend API&lt;br&gt;Flask Application" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture-wrapper">
                    <mxGeometry x="70" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="banditWargame_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture" value="Bandit Wargame&lt;br&gt;Game Server" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture-wrapper">
                    <mxGeometry x="340" y="410" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="frontendApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture" value="Frontend App&lt;br&gt;React" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture-wrapper">
                    <mxGeometry x="70" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="sqliteDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture" value="SQLite Database&lt;br&gt;app.db" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture-wrapper">
                    <mxGeometry x="40" y="440" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="mainApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" value="Main Application&lt;br&gt;Flask Entry Point" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper">
                    <mxGeometry x="69" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" value="AI Mentor&lt;br&gt;AI Integration" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="CoreLogic_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                    <mxGeometry x="190" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="appDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" value="app.db&lt;br&gt;SQLite Database" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="DatabaseModels_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                    <mxGeometry x="40" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="levelScraper_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" value="Level Scraper&lt;br&gt;Data Extraction" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="CoreLogic_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                    <mxGeometry x="340" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="levelsRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" value="Levels Routes&lt;br&gt;API Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="APIRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                    <mxGeometry x="490" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="mentorRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" value="Mentor Routes&lt;br&gt;API Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="APIRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                    <mxGeometry x="340" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="sshManager_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" value="SSH Manager&lt;br&gt;SSH Client" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="CoreLogic_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                    <mxGeometry x="40" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="sshRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" value="SSH Routes&lt;br&gt;API Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="APIRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                    <mxGeometry x="190" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="userModel_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" value="User Model&lt;br&gt;SQLAlchemy ORM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="DatabaseModels_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                    <mxGeometry x="60" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="userRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" value="User Routes&lt;br&gt;API Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="APIRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                    <mxGeometry x="40" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" value="AIMentor&lt;br&gt;AI Integration" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper">
                    <mxGeometry x="340" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="appDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" value="app.db&lt;br&gt;SQLite Database" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper">
                    <mxGeometry x="190" y="790" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="backend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" value="Bandit Backend&lt;br&gt;Flask App" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper">
                    <mxGeometry x="376" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="banditWargame_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" value="Bandit Wargame&lt;br&gt;Game Server" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper">
                    <mxGeometry x="40" y="500" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="externalAI_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" value="External AI API&lt;br&gt;Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper">
                    <mxGeometry x="340" y="440" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" value="Frontend App&lt;br&gt;GUI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper">
                    <mxGeometry x="190" y="310" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="mentorHintRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" value="/api/mentor/hint&lt;br&gt;API Endpoint" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper">
                    <mxGeometry x="226" y="150" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="sshExecuteRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" value="/api/ssh/execute&lt;br&gt;API Endpoint" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper">
                    <mxGeometry x="76" y="180" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="sshManager_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" value="SSHManager&lt;br&gt;SSH Client" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper">
                    <mxGeometry x="40" y="340" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="userModel_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" value="User Model&lt;br&gt;SQLAlchemy ORM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper">
                    <mxGeometry x="190" y="630" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="userRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" value="/api/user/login&lt;br&gt;API Endpoint" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper">
                    <mxGeometry x="190" y="470" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-69983580b1be33b446c4ee837bdf434c-0" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                    <mxGeometry x="175" y="190" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-69983580b1be33b446c4ee837bdf434c-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                    <mxGeometry x="155" y="300" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-69983580b1be33b446c4ee837bdf434c-2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                    <mxGeometry x="702" y="270" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-78d119381d84ebd0cea743684a30dac2-0" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                    <mxGeometry x="541" y="190" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-78d119381d84ebd0cea743684a30dac2-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                    <mxGeometry x="1177" y="290" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-78d119381d84ebd0cea743684a30dac2-2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                    <mxGeometry x="1509" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-78d119381d84ebd0cea743684a30dac2-5" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                    <mxGeometry x="505" y="300" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-78d119381d84ebd0cea743684a30dac2-10" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-78d119381d84ebd0cea743684a30dac2">
                    <mxGeometry x="1916" y="270" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-d13861cdf47babc131626e83fcdf70ab-0" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-d13861cdf47babc131626e83fcdf70ab">
                    <mxGeometry x="278" y="190" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-d13861cdf47babc131626e83fcdf70ab-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-d13861cdf47babc131626e83fcdf70ab">
                    <mxGeometry x="796" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-d13861cdf47babc131626e83fcdf70ab-2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-d13861cdf47babc131626e83fcdf70ab">
                    <mxGeometry x="1491" y="270" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-d13861cdf47babc131626e83fcdf70ab-16" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-d13861cdf47babc131626e83fcdf70ab">
                    <mxGeometry x="248" y="300" width="120" height="60" as="geometry"/>
                  </mxCell>
              <mxCell id="edge-edge-search-to-terminal-69983580b1be33b446c4ee837bdf434c" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="search-results-group-69983580b1be33b446c4ee837bdf434c" target="terminal-69983580b1be33b446c4ee837bdf434c">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-edge-search-to-terminal-69983580b1be33b446c4ee837bdf434c_label" value="Provides context to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-edge-search-to-terminal-69983580b1be33b446c4ee837bdf434c">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-edge-search-to-terminal-78d119381d84ebd0cea743684a30dac2" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="search-results-group-78d119381d84ebd0cea743684a30dac2" target="terminal-78d119381d84ebd0cea743684a30dac2">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-edge-search-to-terminal-78d119381d84ebd0cea743684a30dac2_label" value="Provides context to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-edge-search-to-terminal-78d119381d84ebd0cea743684a30dac2">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-edge-search-to-terminal-d13861cdf47babc131626e83fcdf70ab" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="search-results-group-d13861cdf47babc131626e83fcdf70ab" target="terminal-d13861cdf47babc131626e83fcdf70ab">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-edge-search-to-terminal-d13861cdf47babc131626e83fcdf70ab_label" value="Provides context to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-edge-search-to-terminal-d13861cdf47babc131626e83fcdf70ab">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_frontendApp_backendApp_0_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="frontendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" target="backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_frontendApp_backendApp_0_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture_label" value="WebSockets" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_frontendApp_backendApp_0_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_backendApp_banditServers_1_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" target="banditServers_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_backendApp_banditServers_1_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture_label" value="SSH Connection" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_backendApp_banditServers_1_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_backendApp_aiMentor_2_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" target="aiMentor_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_backendApp_aiMentor_2_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture_label" value="Provides" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_backendApp_aiMentor_2_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-report-section-69983580b1be33b446c4ee837bdf434c-1-to-section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-69983580b1be33b446c4ee837bdf434c-1" target="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-69983580b1be33b446c4ee837bdf434c-1-to-section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-report-section-69983580b1be33b446c4ee837bdf434c-1-to-section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_webSocketClient_0_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="webSocketClient_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_webSocketClient_0_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Manages State" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_webSocketClient_0_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_sshLogin_1_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="sshLogin_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_sshLogin_1_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Renders" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_sshLogin_1_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_levelInfo_2_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="levelInfo_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_levelInfo_2_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Renders" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_levelInfo_2_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_aiMentorComponent_3_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="aiMentorComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_aiMentorComponent_3_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Renders" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_aiMentorComponent_3_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_terminalComponent_4_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="terminalComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_terminalComponent_4_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Renders" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_terminalComponent_4_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_webSocketClient_backendServer_5_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="webSocketClient_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="backendServer_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_webSocketClient_backendServer_5_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Connects to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_webSocketClient_backendServer_5_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_sshLogin_appComponent_6_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="sshLogin_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_sshLogin_appComponent_6_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Triggers SSH Connect" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_sshLogin_appComponent_6_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_terminalComponent_xtermJs_7_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="terminalComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="xtermJs_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_terminalComponent_xtermJs_7_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_terminalComponent_xtermJs_7_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_backendServer_8_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="backendServer_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_backendServer_8_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Emits/Listens Events" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_backendServer_8_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_appComponent_9_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_appComponent_9_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Manages State" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_appComponent_9_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-report-section-69983580b1be33b446c4ee837bdf434c-2-to-section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-69983580b1be33b446c4ee837bdf434c-2" target="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-69983580b1be33b446c4ee837bdf434c-2-to-section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-report-section-69983580b1be33b446c4ee837bdf434c-2-to-section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_frontend_backend_0_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="frontend_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture" target="backend_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_frontend_backend_0_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture_label" value="Requests" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_frontend_backend_0_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_backend_aiMentorModule_1_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="backend_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture" target="aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_backend_aiMentorModule_1_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture_label" value="Encapsulates" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_backend_aiMentorModule_1_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_backend_mentorRoutesModule_2_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="backend_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture" target="mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_backend_mentorRoutesModule_2_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture_label" value="Exposes via API" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_backend_mentorRoutesModule_2_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mentorRoutesModule_aiMentorModule_3_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture" target="aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mentorRoutesModule_aiMentorModule_3_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture_label" value="Utilizes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mentorRoutesModule_aiMentorModule_3_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-report-section-78d119381d84ebd0cea743684a30dac2-1-to-section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-78d119381d84ebd0cea743684a30dac2-1" target="section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture-wrapper">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-78d119381d84ebd0cea743684a30dac2-1-to-section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-report-section-78d119381d84ebd0cea743684a30dac2-1-to-section-diagram-78d119381d84ebd0cea743684a30dac2-High-Level-Architecture-wrapper">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_aiMentorModule_externalAILib_0_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" target="externalAILib_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_aiMentorModule_externalAILib_0_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_aiMentorModule_externalAILib_0_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_aiMentorModule_httpLibs_1_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" target="httpLibs_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_aiMentorModule_httpLibs_1_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_aiMentorModule_httpLibs_1_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mentorRoutesModule_aiMentorModule_2_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" target="aiMentorModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mentorRoutesModule_aiMentorModule_2_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction_label" value="Utilizes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mentorRoutesModule_aiMentorModule_2_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mentorRoutesModule_webFramework_3_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" target="webFramework_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mentorRoutesModule_webFramework_3_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction_label" value="Depends on" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mentorRoutesModule_webFramework_3_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_frontendApp_mentorRoutesModule_4_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="frontendApp_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction" target="mentorRoutesModule_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_frontendApp_mentorRoutesModule_4_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction_label" value="Consumes API" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_frontendApp_mentorRoutesModule_4_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-report-section-78d119381d84ebd0cea743684a30dac2-2-to-section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-78d119381d84ebd0cea743684a30dac2-2" target="section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-78d119381d84ebd0cea743684a30dac2-2-to-section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-report-section-78d119381d84ebd0cea743684a30dac2-2-to-section-diagram-78d119381d84ebd0cea743684a30dac2-Mid-Level-Component-Interaction-wrapper">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_banditAIMentorClass_initMethod_0_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="initMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_banditAIMentorClass_initMethod_0_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Initializes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_banditAIMentorClass_initMethod_0_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_banditAIMentorClass_getResponseMethod_1_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="getResponseMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_banditAIMentorClass_getResponseMethod_1_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Uses OpenAI API" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_banditAIMentorClass_getResponseMethod_1_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_banditAIMentorClass_clearConversationMethod_2_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="clearConversationMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_banditAIMentorClass_clearConversationMethod_2_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Manages history" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_banditAIMentorClass_clearConversationMethod_2_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_banditAIMentorClass_getLevelHintMethod_3_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="getLevelHintMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_banditAIMentorClass_getLevelHintMethod_3_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Provides static hint" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_banditAIMentorClass_getLevelHintMethod_3_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_banditAIMentorClass_explainCommandMethod_4_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="explainCommandMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_banditAIMentorClass_explainCommandMethod_4_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Provides static explanation" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_banditAIMentorClass_explainCommandMethod_4_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_globalInstance_banditAIMentorClass_5_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="globalInstance_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="banditAIMentorClass_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_globalInstance_banditAIMentorClass_5_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Is instance of" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_globalInstance_banditAIMentorClass_5_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_initSocketIOFunc_socketIOMessage_6_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="initSocketIOFunc_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="socketIOMessage_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_initSocketIOFunc_socketIOMessage_6_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Registers" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_initSocketIOFunc_socketIOMessage_6_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_initSocketIOFunc_socketIOClear_7_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="initSocketIOFunc_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="socketIOClear_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_initSocketIOFunc_socketIOClear_7_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Registers" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_initSocketIOFunc_socketIOClear_7_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_socketIOMessage_getResponseMethod_8_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="socketIOMessage_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="getResponseMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_socketIOMessage_getResponseMethod_8_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Calls" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_socketIOMessage_getResponseMethod_8_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_socketIOClear_clearConversationMethod_9_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="socketIOClear_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="clearConversationMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_socketIOClear_clearConversationMethod_9_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Calls" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_socketIOClear_clearConversationMethod_9_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_hintRoute_getLevelHintMethod_10_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="hintRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="getLevelHintMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_hintRoute_getLevelHintMethod_10_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Calls" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_hintRoute_getLevelHintMethod_10_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_explainRoute_explainCommandMethod_11_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="explainRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="explainCommandMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_explainRoute_explainCommandMethod_11_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Calls" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_explainRoute_explainCommandMethod_11_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_chatRoute_getResponseMethod_12_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="chatRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="getResponseMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_chatRoute_getResponseMethod_12_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Calls" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_chatRoute_getResponseMethod_12_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_clearRoute_clearConversationMethod_13_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="clearRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="clearConversationMethod_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_clearRoute_clearConversationMethod_13_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Calls" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_clearRoute_clearConversationMethod_13_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mentorBlueprint_hintRoute_14_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mentorBlueprint_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="hintRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mentorBlueprint_hintRoute_14_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Defines" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mentorBlueprint_hintRoute_14_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mentorBlueprint_explainRoute_15_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mentorBlueprint_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="explainRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mentorBlueprint_explainRoute_15_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Defines" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mentorBlueprint_explainRoute_15_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mentorBlueprint_chatRoute_16_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mentorBlueprint_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="chatRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mentorBlueprint_chatRoute_16_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Defines" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mentorBlueprint_chatRoute_16_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mentorBlueprint_clearRoute_17_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mentorBlueprint_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details" target="clearRoute_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mentorBlueprint_clearRoute_17_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details_label" value="Defines" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mentorBlueprint_clearRoute_17_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-report-section-78d119381d84ebd0cea743684a30dac2-5-to-section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-78d119381d84ebd0cea743684a30dac2-5" target="section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details-wrapper">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-78d119381d84ebd0cea743684a30dac2-5-to-section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-report-section-78d119381d84ebd0cea743684a30dac2-5-to-section-diagram-78d119381d84ebd0cea743684a30dac2-Low-Level-Implementation-Details-wrapper">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_banditAIMentor_openAIApi_0_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="banditAIMentor_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" target="openAIApi_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_banditAIMentor_openAIApi_0_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion_label" value="Interacts with" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_banditAIMentor_openAIApi_0_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mentorRoutes_socketIO_1_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mentorRoutes_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" target="socketIO_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mentorRoutes_socketIO_1_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion_label" value="Exposes via" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mentorRoutes_socketIO_1_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mentorRoutes_restApi_2_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mentorRoutes_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" target="restApi_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mentorRoutes_restApi_2_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion_label" value="Exposes via" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mentorRoutes_restApi_2_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mentorRoutes_banditAIMentor_3_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mentorRoutes_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" target="banditAIMentor_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mentorRoutes_banditAIMentor_3_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion_label" value="Utilizes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mentorRoutes_banditAIMentor_3_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_frontendApp_socketIO_4_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="frontendApp_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" target="socketIO_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_frontendApp_socketIO_4_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion_label" value="Communicates via" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_frontendApp_socketIO_4_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_frontendApp_restApi_5_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="frontendApp_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" target="restApi_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_frontendApp_restApi_5_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion_label" value="Communicates via" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_frontendApp_restApi_5_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_socketIO_banditAIMentor_6_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="socketIO_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" target="banditAIMentor_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_socketIO_banditAIMentor_6_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion_label" value="Provides chat" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_socketIO_banditAIMentor_6_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_restApi_banditAIMentor_7_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="restApi_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion" target="banditAIMentor_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_restApi_banditAIMentor_7_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion_label" value="Provides hints/chat" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_restApi_banditAIMentor_7_78d119381d84ebd0cea743684a30dac2_section_section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-report-section-78d119381d84ebd0cea743684a30dac2-10-to-section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-78d119381d84ebd0cea743684a30dac2-10" target="section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-78d119381d84ebd0cea743684a30dac2-10-to-section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-report-section-78d119381d84ebd0cea743684a30dac2-10-to-section-diagram-78d119381d84ebd0cea743684a30dac2-Conclusion-wrapper">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_frontendApp_banditBackend_0_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="frontendApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture" target="banditBackend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_frontendApp_banditBackend_0_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture_label" value="API Calls" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_frontendApp_banditBackend_0_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_banditBackend_banditWargame_1_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="banditBackend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture" target="banditWargame_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_banditBackend_banditWargame_1_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture_label" value="SSH" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_banditBackend_banditWargame_1_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_banditBackend_sqliteDB_2_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="banditBackend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture" target="sqliteDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_banditBackend_sqliteDB_2_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture_label" value="Manages" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_banditBackend_sqliteDB_2_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_banditBackend_aiMentor_3_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="banditBackend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture" target="aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_banditBackend_aiMentor_3_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture_label" value="Integrates" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_banditBackend_aiMentor_3_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-report-section-d13861cdf47babc131626e83fcdf70ab-1-to-section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-d13861cdf47babc131626e83fcdf70ab-1" target="section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture-wrapper">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-d13861cdf47babc131626e83fcdf70ab-1-to-section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-report-section-d13861cdf47babc131626e83fcdf70ab-1-to-section-diagram-d13861cdf47babc131626e83fcdf70ab-High-Level-Architecture-wrapper">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mainApp_sshRoutes_0_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mainApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" target="sshRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mainApp_sshRoutes_0_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown_label" value="Registers" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mainApp_sshRoutes_0_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mainApp_mentorRoutes_1_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mainApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" target="mentorRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mainApp_mentorRoutes_1_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown_label" value="Registers" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mainApp_mentorRoutes_1_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mainApp_levelsRoutes_2_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mainApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" target="levelsRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mainApp_levelsRoutes_2_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown_label" value="Registers" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mainApp_levelsRoutes_2_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mainApp_userRoutes_3_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mainApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" target="userRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mainApp_userRoutes_3_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown_label" value="Registers" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mainApp_userRoutes_3_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_sshRoutes_sshManager_4_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="sshRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" target="sshManager_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_sshRoutes_sshManager_4_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_sshRoutes_sshManager_4_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mentorRoutes_aiMentor_5_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mentorRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" target="aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mentorRoutes_aiMentor_5_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mentorRoutes_aiMentor_5_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_levelsRoutes_levelScraper_6_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="levelsRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" target="levelScraper_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_levelsRoutes_levelScraper_6_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_levelsRoutes_levelScraper_6_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_userRoutes_userModel_7_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="userRoutes_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" target="userModel_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_userRoutes_userModel_7_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_userRoutes_userModel_7_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_userModel_appDB_8_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="userModel_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" target="appDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_userModel_appDB_8_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown_label" value="Interacts with" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_userModel_appDB_8_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mainApp_appDB_9_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mainApp_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown" target="appDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mainApp_appDB_9_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown_label" value="Initializes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mainApp_appDB_9_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-report-section-d13861cdf47babc131626e83fcdf70ab-2-to-section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-d13861cdf47babc131626e83fcdf70ab-2" target="section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-d13861cdf47babc131626e83fcdf70ab-2-to-section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-report-section-d13861cdf47babc131626e83fcdf70ab-2-to-section-diagram-d13861cdf47babc131626e83fcdf70ab-Mid-Level-Component-Breakdown-wrapper">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_frontend_userRoute_0_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" target="userRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_frontend_userRoute_0_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details_label" value="POST /api/user/login" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_frontend_userRoute_0_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_userRoute_userModel_1_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="userRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" target="userModel_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_userRoute_userModel_1_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details_label" value="Validates against" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_userRoute_userModel_1_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_userModel_appDB_2_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="userModel_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" target="appDB_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_userModel_appDB_2_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details_label" value="Queries" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_userModel_appDB_2_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_userRoute_frontend_3_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="userRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" target="frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_userRoute_frontend_3_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details_label" value="Returns response" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_userRoute_frontend_3_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_frontend_sshExecuteRoute_4_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" target="sshExecuteRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_frontend_sshExecuteRoute_4_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details_label" value="POST /api/ssh/execute" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_frontend_sshExecuteRoute_4_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_sshExecuteRoute_sshManager_5_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="sshExecuteRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" target="sshManager_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_sshExecuteRoute_sshManager_5_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details_label" value="Calls execute_command" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_sshExecuteRoute_sshManager_5_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_sshManager_banditWargame_6_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="sshManager_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" target="banditWargame_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_sshManager_banditWargame_6_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details_label" value="Executes command via Paramiko" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_sshManager_banditWargame_6_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_sshExecuteRoute_frontend_7_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="sshExecuteRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" target="frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_sshExecuteRoute_frontend_7_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details_label" value="Returns output" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_sshExecuteRoute_frontend_7_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_frontend_mentorHintRoute_8_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" target="mentorHintRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_frontend_mentorHintRoute_8_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details_label" value="POST /api/mentor/hint" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_frontend_mentorHintRoute_8_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mentorHintRoute_aiMentor_9_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mentorHintRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" target="aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mentorHintRoute_aiMentor_9_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details_label" value="Calls get_hint" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mentorHintRoute_aiMentor_9_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_aiMentor_externalAI_10_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="aiMentor_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" target="externalAI_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_aiMentor_externalAI_10_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details_label" value="Interacts with" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_aiMentor_externalAI_10_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_mentorHintRoute_frontend_11_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="mentorHintRoute_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details" target="frontend_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_mentorHintRoute_frontend_11_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details_label" value="Returns hint" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_mentorHintRoute_frontend_11_d13861cdf47babc131626e83fcdf70ab_section_section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-report-section-d13861cdf47babc131626e83fcdf70ab-16-to-section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-d13861cdf47babc131626e83fcdf70ab-16" target="section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-d13861cdf47babc131626e83fcdf70ab-16-to-section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-report-section-d13861cdf47babc131626e83fcdf70ab-16-to-section-diagram-d13861cdf47babc131626e83fcdf70ab-Implementation-Details-wrapper">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-search-result-69983580b1be33b446c4ee837bdf434c-to-report-section-69983580b1be33b446c4ee837bdf434c-0" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="search-result-69983580b1be33b446c4ee837bdf434c" target="report-section-69983580b1be33b446c4ee837bdf434c-0">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
<mxCell id="edge-report-section-69983580b1be33b446c4ee837bdf434c-0-to-report-section-69983580b1be33b446c4ee837bdf434c-1" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-69983580b1be33b446c4ee837bdf434c-0" target="report-section-69983580b1be33b446c4ee837bdf434c-1">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
<mxCell id="edge-report-section-69983580b1be33b446c4ee837bdf434c-0-to-report-section-69983580b1be33b446c4ee837bdf434c-2" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-69983580b1be33b446c4ee837bdf434c-0" target="report-section-69983580b1be33b446c4ee837bdf434c-2">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
<mxCell id="edge-search-result-78d119381d84ebd0cea743684a30dac2-to-report-section-78d119381d84ebd0cea743684a30dac2-0" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="search-result-78d119381d84ebd0cea743684a30dac2" target="report-section-78d119381d84ebd0cea743684a30dac2-0">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
<mxCell id="edge-report-section-78d119381d84ebd0cea743684a30dac2-0-to-report-section-78d119381d84ebd0cea743684a30dac2-1" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-78d119381d84ebd0cea743684a30dac2-0" target="report-section-78d119381d84ebd0cea743684a30dac2-1">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
<mxCell id="edge-report-section-78d119381d84ebd0cea743684a30dac2-0-to-report-section-78d119381d84ebd0cea743684a30dac2-2" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-78d119381d84ebd0cea743684a30dac2-0" target="report-section-78d119381d84ebd0cea743684a30dac2-2">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
<mxCell id="edge-report-section-78d119381d84ebd0cea743684a30dac2-0-to-report-section-78d119381d84ebd0cea743684a30dac2-5" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-78d119381d84ebd0cea743684a30dac2-0" target="report-section-78d119381d84ebd0cea743684a30dac2-5">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
<mxCell id="edge-report-section-78d119381d84ebd0cea743684a30dac2-0-to-report-section-78d119381d84ebd0cea743684a30dac2-10" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-78d119381d84ebd0cea743684a30dac2-0" target="report-section-78d119381d84ebd0cea743684a30dac2-10">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
<mxCell id="edge-search-result-d13861cdf47babc131626e83fcdf70ab-to-report-section-d13861cdf47babc131626e83fcdf70ab-0" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="search-result-d13861cdf47babc131626e83fcdf70ab" target="report-section-d13861cdf47babc131626e83fcdf70ab-0">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
<mxCell id="edge-report-section-d13861cdf47babc131626e83fcdf70ab-0-to-report-section-d13861cdf47babc131626e83fcdf70ab-1" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-d13861cdf47babc131626e83fcdf70ab-0" target="report-section-d13861cdf47babc131626e83fcdf70ab-1">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
<mxCell id="edge-report-section-d13861cdf47babc131626e83fcdf70ab-0-to-report-section-d13861cdf47babc131626e83fcdf70ab-2" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-d13861cdf47babc131626e83fcdf70ab-0" target="report-section-d13861cdf47babc131626e83fcdf70ab-2">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
<mxCell id="edge-report-section-d13861cdf47babc131626e83fcdf70ab-0-to-report-section-d13861cdf47babc131626e83fcdf70ab-16" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-d13861cdf47babc131626e83fcdf70ab-0" target="report-section-d13861cdf47babc131626e83fcdf70ab-16">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
            </root>
          </mxGraphModel>
        </diagram>
      </mxfile>