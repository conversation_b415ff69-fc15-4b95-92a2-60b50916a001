"""
API Routing Fix for Bandit Wargame GUI

This script helps identify and fix API routing issues in the Bandit Wargame GUI application.
It can be used to diagnose and repair common routing problems between the frontend and backend.
"""
import os
import sys
import re
import json
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Set, Pattern

class APIRoutingFixer:
    """Class to fix API routing issues in the Bandit Wargame GUI"""
    
    def __init__(self, base_dir: str = None):
        """Initialize the API routing fixer"""
        self.base_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
        self.backend_dir = os.path.join(self.base_dir, "backend", "bandit-backend")
        self.frontend_dir = os.path.join(self.base_dir, "frontend", "bandit-frontend", "src")
        
        # Common API route patterns
        self.api_patterns = {
            'flask': r'@app\.route\([\s\S]*?\'([^\']+)\'',
            'fastapi': r'@app\.(get|post|put|delete|patch|options|head)\([\s\S]*?\'([^\']+)\'',
            'axios': r'\.(get|post|put|delete|patch|options|head)\([\s\S]*?([\'\"])(/[^\'\"]+)\2',
            'fetch': r'fetch\([\s\S]*?([\'\"])(/[^\'\"]+)\1',
        }
        
        # Known API endpoints
        self.known_endpoints = {
            '/api/health': {'methods': ['GET'], 'description': 'Health check endpoint'},
            '/api/ssh/connect': {'methods': ['POST'], 'description': 'SSH connection endpoint'},
            '/api/ssh/command': {'methods': ['POST'], 'description': 'SSH command execution'},
            '/api/levels': {'methods': ['GET'], 'description': 'Get all levels'},
            '/api/levels/<int:level_id>': {'methods': ['GET'], 'description': 'Get level details'},
            '/api/mentor/hint': {'methods': ['POST'], 'description': 'Get a hint from the AI mentor'},
            '/api/mentor/explain': {'methods': ['POST'], 'description': 'Get an explanation from the AI mentor'},
            '/socket.io/': {'methods': ['GET', 'POST'], 'description': 'WebSocket endpoint'},
        }
        
        # Results storage
        self.issues: List[Dict[str, Any]] = []
        self.fixes_applied: List[Dict[str, str]] = []
    
    def find_files(self, directory: str, extensions: List[str]) -> List[str]:
        """Find all files with the given extensions in the directory"""
        files = []
        for ext in extensions:
            for root, _, filenames in os.walk(directory):
                for filename in filenames:
                    if filename.endswith(ext):
                        files.append(os.path.join(root, filename))
        return files
    
    def scan_for_routes(self) -> Dict[str, List[Dict[str, Any]]]:
        """Scan the codebase for API routes"""
        print("Scanning for API routes...")
        
        backend_files = self.find_files(self.backend_dir, ['.py'])
        frontend_files = self.find_files(self.frontend_dir, ['.js', '.jsx', '.ts', '.tsx'])
        
        routes = {
            'backend': self._scan_files_for_routes(backend_files, ['flask', 'fastapi']),
            'frontend': self._scan_files_for_routes(frontend_files, ['axios', 'fetch'])
        }
        
        return routes
    
    def _scan_files_for_routes(self, files: List[str], route_types: List[str]) -> List[Dict[str, Any]]:
        """Scan files for routes of the specified types"""
        routes = []
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for route_type in route_types:
                    pattern = self.api_patterns.get(route_type)
                    if not pattern:
                        continue
                    
                    matches = re.finditer(pattern, content, re.MULTILINE)
                    
                    for match in matches:
                        # Extract the URL from the match
                        url = None
                        if route_type in ['flask', 'fastapi']:
                            url = match.group(1)
                        elif route_type in ['axios', 'fetch']:
                            # For axios/fetch, the URL is in the last group
                            url = match.group(match.lastindex)
                        
                        if not url or not url.startswith('/'):
                            continue
                        
                        # Get the HTTP method if available
                        http_method = 'GET'  # Default to GET
                        if route_type == 'fastapi':
                            http_method = match.group(1).upper()
                        elif route_type == 'axios':
                            http_method = match.group(1).upper()
                        
                        # Get line number
                        line_number = content[:match.start()].count('\n') + 1
                        
                        routes.append({
                            'file': file_path,
                            'line': line_number,
                            'type': route_type,
                            'method': http_method,
                            'url': url,
                            'match': match.group(0).strip()
                        })
                        
            except Exception as e:
                print(f"Error scanning file {file_path}: {str(e)}", file=sys.stderr)
        
        return routes
    
    def analyze_routes(self, routes: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Analyze routes for potential issues"""
        print("Analyzing routes for issues...")
        
        backend_routes = routes.get('backend', [])
        frontend_routes = routes.get('frontend', [])
        
        # Check for undefined backend routes
        backend_urls = {route['url'] for route in backend_routes}
        frontend_urls = {route['url'] for route in frontend_routes}
        
        # Find frontend routes with no matching backend route
        undefined_routes = []
        for route in frontend_routes:
            # Skip non-API routes
            if not route['url'].startswith('/api/'):
                continue
                
            # Check if the route exists in the backend
            if not any(self._routes_match(route['url'], backend_url) for backend_url in backend_urls):
                undefined_routes.append(route)
        
        # Find deprecated or removed backend routes
        deprecated_routes = []
        for route in backend_routes:
            if route['url'] not in self.known_endpoints and not any(
                self._routes_match(route['url'], known_url) 
                for known_url in self.known_endpoints
            ):
                deprecated_routes.append(route)
        
        # Find inconsistent method usage
        inconsistent_methods = []
        for route in frontend_routes:
            # Skip non-API routes
            if not route['url'].startswith('/api/'):
                continue
                
            # Find matching backend routes
            matching_backend = [
                r for r in backend_routes 
                if self._routes_match(route['url'], r['url'])
            ]
            
            if not matching_backend:
                continue
                
            # Check if the method is allowed for this route
            for backend_route in matching_backend:
                if route['method'] not in self.known_endpoints.get(backend_route['url'], {}).get('methods', ['GET']):
                    inconsistent_methods.append({
                        'frontend': route,
                        'backend': backend_route,
                        'expected_methods': self.known_endpoints.get(backend_route['url'], {}).get('methods', ['GET'])
                    })
        
        # Find duplicate route definitions
        route_counts = {}
        for route in backend_routes:
            key = f"{route['method']} {route['url']}"
            if key not in route_counts:
                route_counts[key] = []
            route_counts[key].append(route)
        
        duplicate_routes = [routes for key, routes in route_counts.items() if len(routes) > 1]
        
        return {
            'undefined_routes': undefined_routes,
            'deprecated_routes': deprecated_routes,
            'inconsistent_methods': inconsistent_methods,
            'duplicate_routes': duplicate_routes,
            'backend_routes': backend_routes,
            'frontend_routes': frontend_routes
        }
    
    def _routes_match(self, url1: str, url2: str) -> bool:
        """Check if two route URLs match, accounting for route parameters"""
        # Simple exact match
        if url1 == url2:
            return True
        
        # Check for route parameters (e.g., /users/<id> vs /users/123)
        parts1 = url1.split('/')
        parts2 = url2.split('/')
        
        if len(parts1) != len(parts2):
            return False
        
        for p1, p2 in zip(parts1, parts2):
            # If parts are different and neither is a parameter placeholder
            if p1 != p2 and not (p1.startswith('<') and p1.endswith('>')) and not (p2.startswith('<') and p2.endswith('>')):
                return False
        
        return True
    
    def generate_fixes(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate fixes for the identified issues"""
        fixes = []
        
        # Fixes for undefined routes
        for route in analysis.get('undefined_routes', []):
            # Check if this is a known endpoint with a different URL
            known_match = None
            for known_url, details in self.known_endpoints.items():
                if self._routes_match(route['url'], known_url):
                    known_match = (known_url, details)
                    break
            
            if known_match:
                # Suggest updating the frontend to use the correct URL
                known_url, details = known_match
                fixes.append({
                    'type': 'update_frontend_url',
                    'file': route['file'],
                    'line': route['line'],
                    'old_url': route['url'],
                    'new_url': known_url,
                    'description': f"Update frontend to use the correct API endpoint: {known_url}",
                    'severity': 'high',
                    'auto_fixable': True
                })
            else:
                # No known matching endpoint, suggest adding it to the backend
                fixes.append({
                    'type': 'add_backend_route',
                    'url': route['url'],
                    'method': route['method'],
                    'description': f"Add missing backend route: {route['method']} {route['url']}",
                    'severity': 'high',
                    'auto_fixable': False
                })
        
        # Fixes for deprecated routes
        for route in analysis.get('deprecated_routes', []):
            # Check if this is a known endpoint with a different URL
            known_match = None
            for known_url, details in self.known_endpoints.items():
                if self._routes_match(route['url'], known_url):
                    known_match = (known_url, details)
                    break
            
            if known_match:
                # This is a known endpoint with a different URL pattern
                known_url, details = known_match
                fixes.append({
                    'type': 'update_route_definition',
                    'file': route['file'],
                    'line': route['line'],
                    'old_url': route['url'],
                    'new_url': known_url,
                    'description': f"Update route to use the standard URL pattern: {known_url}",
                    'severity': 'medium',
                    'auto_fixable': True
                })
            else:
                # This is a truly deprecated route
                fixes.append({
                    'type': 'mark_route_deprecated',
                    'file': route['file'],
                    'line': route['line'],
                    'url': route['url'],
                    'description': f"Mark route as deprecated: {route['method']} {route['url']}",
                    'severity': 'low',
                    'auto_fixable': True
                })
        
        # Fixes for inconsistent methods
        for issue in analysis.get('inconsistent_methods', []):
            frontend = issue['frontend']
            backend = issue['backend']
            expected_methods = issue['expected_methods']
            
            if frontend['method'] in expected_methods:
                # The method is allowed, but might be using the wrong URL
                fixes.append({
                    'type': 'update_frontend_url',
                    'file': frontend['file'],
                    'line': frontend['line'],
                    'old_url': frontend['url'],
                    'new_url': backend['url'],
                    'description': f"Update frontend to use the correct URL: {backend['url']}",
                    'severity': 'high',
                    'auto_fixable': True
                })
            else:
                # The method is not allowed for this URL
                fixes.append({
                    'type': 'update_http_method',
                    'file': frontend['file'],
                    'line': frontend['line'],
                    'url': frontend['url'],
                    'old_method': frontend['method'],
                    'new_method': expected_methods[0],
                    'description': f"Update HTTP method from {frontend['method']} to {expected_methods[0]} for {frontend['url']}",
                    'severity': 'high',
                    'auto_fixable': True
                })
        
        # Fixes for duplicate routes
        for duplicate_group in analysis.get('duplicate_routes', []):
            # Keep the first definition and mark others as duplicates
            primary = duplicate_group[0]
            for duplicate in duplicate_group[1:]:
                fixes.append({
                    'type': 'remove_duplicate_route',
                    'file': duplicate['file'],
                    'line': duplicate['line'],
                    'url': duplicate['url'],
                    'method': duplicate['method'],
                    'description': f"Remove duplicate route definition for {duplicate['method']} {duplicate['url']}",
                    'severity': 'medium',
                    'auto_fixable': True,
                    'primary_location': f"{primary['file']}:{primary['line']}"
                })
        
        return fixes
    
    def apply_fixes(self, fixes: List[Dict[str, Any]], dry_run: bool = True) -> List[Dict[str, str]]:
        """Apply the suggested fixes"""
        applied_fixes = []
        
        for fix in fixes:
            if not fix.get('auto_fixable', False):
                print(f"Skipping non-auto-fixable issue: {fix['description']}")
                continue
            
            if dry_run:
                print(f"[DRY RUN] Would apply fix: {fix['description']}")
                applied_fixes.append({
                    'type': fix['type'],
                    'description': fix['description'],
                    'status': 'skipped (dry run)'
                })
                continue
            
            try:
                if fix['type'] == 'update_frontend_url':
                    self._update_file(
                        fix['file'],
                        fix['line'],
                        fix['old_url'],
                        fix['new_url']
                    )
                elif fix['type'] == 'update_http_method':
                    self._update_http_method(
                        fix['file'],
                        fix['line'],
                        fix['url'],
                        fix['old_method'],
                        fix['new_method']
                    )
                elif fix['type'] in ['remove_duplicate_route', 'mark_route_deprecated']:
                    self._comment_out_line(fix['file'], fix['line'])
                
                applied_fixes.append({
                    'type': fix['type'],
                    'description': fix['description'],
                    'status': 'applied'
                })
                print(f"Applied fix: {fix['description']}")
                
            except Exception as e:
                applied_fixes.append({
                    'type': fix['type'],
                    'description': fix['description'],
                    'status': f'failed: {str(e)}'
                })
                print(f"Failed to apply fix: {fix['description']} - {str(e)}", file=sys.stderr)
        
        return applied_fixes
    
    def _update_file(self, file_path: str, line_number: int, old_text: str, new_text: str):
        """Update a line in a file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if line_number < 1 or line_number > len(lines):
            raise ValueError(f"Line number {line_number} is out of range for file {file_path}")
        
        # Replace the old text with the new text in the specified line
        lines[line_number - 1] = lines[line_number - 1].replace(old_text, new_text)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
    
    def _update_http_method(self, file_path: str, line_number: int, url: str, old_method: str, new_method: str):
        """Update the HTTP method in a frontend API call"""
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if line_number < 1 or line_number > len(lines):
            raise ValueError(f"Line number {line_number} is out of range for file {file_path}")
        
        line = lines[line_number - 1]
        
        # Replace the HTTP method in the line
        updated_line = re.sub(
            rf'(\.(get|post|put|delete|patch|options|head)\s*\()',
            f'.{new_method.lower()}(',
            line,
            flags=re.IGNORECASE
        )
        
        if updated_line == line:
            raise ValueError(f"Could not find HTTP method to update in line: {line.strip()}")
        
        lines[line_number - 1] = updated_line
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
    
    def _comment_out_line(self, file_path: str, line_number: int):
        """Comment out a line in a file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if line_number < 1 or line_number > len(lines):
            raise ValueError(f"Line number {line_number} is out of range for file {file_path}")
        
        # Add a comment character based on file extension
        ext = os.path.splitext(file_path)[1].lower()
        if ext in ['.py']:
            comment = '# '
        elif ext in ['.js', '.jsx', '.ts', '.tsx']:
            comment = '// '
        else:
            comment = '# '
        
        # Only add the comment if it's not already there
        line = lines[line_number - 1].lstrip()
        if not line.startswith(comment.strip()) and not line.startswith('#'):
            lines[line_number - 1] = f"{comment}{lines[line_number - 1]}"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
    
    def generate_report(self, analysis: Dict[str, Any], output_file: str = None) -> str:
        """Generate a report of the analysis"""
        report = {
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
            'issues_found': {
                'undefined_routes': len(analysis.get('undefined_routes', [])),
                'deprecated_routes': len(analysis.get('deprecated_routes', [])),
                'inconsistent_methods': len(analysis.get('inconsistent_methods', [])),
                'duplicate_routes': len(analysis.get('duplicate_routes', [])),
            },
            'backend_routes': [
                {
                    'file': os.path.relpath(r['file'], self.base_dir),
                    'line': r['line'],
                    'method': r['method'],
                    'url': r['url'],
                    'type': r['type']
                }
                for r in analysis.get('backend_routes', [])
            ],
            'frontend_routes': [
                {
                    'file': os.path.relpath(r['file'], self.base_dir),
                    'line': r['line'],
                    'method': r['method'],
                    'url': r['url'],
                    'type': r['type']
                }
                for r in analysis.get('frontend_routes', [])
            ]
        }
        
        if not output_file:
            output_file = os.path.join(self.base_dir, 'api_routing_report.json')
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2)
        
        # Print a summary
        print(f"\nAPI Routing Analysis Report")
        print("=" * 50)
        print(f"Backend routes found: {len(report['backend_routes'])}")
        print(f"Frontend API calls found: {len(report['frontend_routes'])}")
        print("\nIssues Found:")
        print(f"- Undefined routes: {report['issues_found']['undefined_routes']}")
        print(f"- Deprecated routes: {report['issues_found']['deprecated_routes']}")
        print(f"- Inconsistent HTTP methods: {report['issues_found']['inconsistent_methods']}")
        print(f"- Duplicate route definitions: {report['issues_found']['duplicate_routes']}")
        print(f"\nFull report saved to: {os.path.abspath(output_file)}")
        
        return output_file

def main():
    """Main function to run the API routing fixer"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Fix API routing issues in Bandit Wargame GUI')
    parser.add_argument('--dir', type=str, default=None,
                        help='Base directory of the project (default: current directory)')
    parser.add_argument('--fix', action='store_true',
                        help='Apply fixes automatically (default: dry run)')
    parser.add_argument('--output', type=str, default=None,
                        help='Output file for the report (default: api_routing_report.json)')
    
    args = parser.parse_args()
    
    fixer = APIRoutingFixer(base_dir=args.dir)
    
    # Scan for routes
    print("Scanning for API routes...")
    routes = fixer.scan_for_routes()
    
    # Analyze routes
    print("Analyzing routes for issues...")
    analysis = fixer.analyze_routes(routes)
    
    # Generate fixes
    print("Generating fixes...")
    fixes = fixer.generate_fixes(analysis)
    
    # Apply fixes if requested
    if args.fix:
        print("\nApplying fixes...")
        applied_fixes = fixer.apply_fixes(fixes, dry_run=False)
        
        # Print summary of applied fixes
        print("\nApplied Fixes:")
        for fix in applied_fixes:
            print(f"- {fix['description']}: {fix['status']}")
    else:
        print("\nNo changes made (use --fix to apply changes)")
        print("\nSuggested Fixes:")
        for i, fix in enumerate(fixes, 1):
            auto_fixable = "(auto-fixable)" if fix.get('auto_fixable', False) else "(manual fix required)"
            print(f"{i}. {fix['description']} {auto_fixable}")
    
    # Generate report
    report_file = fixer.generate_report(analysis, args.output)
    print(f"\nReport generated: {os.path.abspath(report_file)}")
    
    # Exit with non-zero status if there are critical issues
    critical_issues = sum(1 for f in fixes if f.get('severity') == 'high')
    if critical_issues > 0:
        print(f"\nWARNING: Found {critical_issues} critical issue(s) that need attention!", file=sys.stderr)
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
