# Bandit Wargame GUI - API Reference

This document provides detailed documentation for all API endpoints available in the Bandit Wargame GUI application. The API follows RESTful conventions and uses JSON for request/response bodies.

## Base URL

All API endpoints are prefixed with `/api`.

```
http://localhost:5001/api
```

## Authentication

Most endpoints require authentication. Include the session token in the request headers:

```
Authorization: Bearer <your_session_token>
```

## WebSocket Endpoints

The application uses WebSockets for real-time communication. Connect to:

```
ws://localhost:5001/socket.io/
```

## API Endpoints

### Health Check

#### GET /api/health

Check if the API is running.

**Response**
```json
{
  "status": "healthy",
  "timestamp": "2025-02-28T12:00:00Z"
}
```

#### GET /api/health/detailed

Get detailed system health information.

**Response**
```json
{
  "status": "healthy",
  "timestamp": "2025-02-28T12:00:00Z",
  "system": {
    "cpu_percent": 25.5,
    "memory_percent": 45.2,
    "disk_usage": 30.1,
    "uptime": 12345
  }
}
```

### SSH Management

#### WebSocket Events

- **ssh_connect**: Connect to an SSH server
  ```json
  {
    "hostname": "bandit.labs.overthewire.org",
    "port": 2220,
    "username": "bandit0",
    "password": "bandit0",
    "session_id": "unique_session_id"
  }
  ```

- **ssh_command**: Send a command to the SSH server
  ```json
  {
    "session_id": "unique_session_id",
    "command": "ls -la"
  }
  ```

- **ssh_disconnect**: Disconnect from the SSH server
  ```json
  {
    "session_id": "unique_session_id"
  }
  ```

#### REST Endpoints

##### GET /api/ssh/sessions

List all active SSH sessions.

**Response**
```json
{
  "sessions": ["session1", "session2"]
}
```

##### GET /api/ssh/session/{session_id}/status

Get status of a specific SSH session.

**Response**
```json
{
  "session_id": "unique_session_id",
  "connected": true,
  "hostname": "bandit.labs.overthewire.org",
  "username": "bandit0"
}
```

### Level Management

#### GET /api/levels/all

Get all available levels.

**Response**
```json
{
  "success": true,
  "levels": {
    "0": {
      "level": 0,
      "username": "bandit0",
      "password": "bandit0",
      "objective": "Find the password for the next level"
    },
    "1": {
      "level": 1,
      "username": "bandit1",
      "password": "",
      "objective": "Find the password in a file"
    }
  },
  "count": 2
}
```

#### GET /api/levels/{level_num}

Get specific level information.

**Response**
```json
{
  "success": true,
  "level": {
    "level": 0,
    "username": "bandit0",
    "password": "bandit0",
    "objective": "Find the password for the next level"
  }
}
```

#### POST /api/levels/refresh

Refresh level data by scraping the OverTheWire website.

**Response**
```json
{
  "success": true,
  "message": "Levels data refreshed successfully",
  "count": 30
}
```

#### GET /api/levels/search?q={query}

Search levels by keyword.

**Parameters**
- `q` (string, required): Search query

**Response**
```json
{
  "success": true,
  "results": [
    {
      "level": 0,
      "username": "bandit0",
      "matches": ["objective", "hint"]
    }
  ]
}
```

#### POST /api/levels/detect

Detect current level based on terminal output.

**Request Body**
```json
{
  "terminal_output": "bandit0@bandit:~$"
}
```

**Response**
```json
{
  "success": true,
  "detected_level": 0,
  "confidence": 0.95
}
```

### AI Mentor

#### WebSocket Events

- **mentor_message**: Send a message to the AI mentor
  ```json
  {
    "message": "How do I solve this level?",
    "session_id": "unique_session_id",
    "current_level": 0,
    "recent_commands": ["ls", "cat readme"],
    "terminal_output": "bandit0@bandit:~$ cat readme\nboJ9jbbUNNfktd78OOpsqOltutMc3MY1"
  }
  ```

- **mentor_clear**: Clear conversation history
  ```json
  {
    "session_id": "unique_session_id"
  }
  ```

#### REST Endpoints

##### GET /api/mentor/hint/{level_num}

Get a hint for a specific level.

**Response**
```json
{
  "success": true,
  "level": 0,
  "hint": "Check the contents of the home directory for a file containing the password."
}
```

##### GET /api/mentor/explain/{command}

Get an explanation for a Linux command.

**Response**
```json
{
  "success": true,
  "command": "ls -la",
  "explanation": "The 'ls -la' command lists all files and directories in the current directory, including hidden ones, with detailed information."
}
```

##### POST /api/mentor/chat

Chat with the AI mentor (REST alternative to WebSocket).

**Request Body**
```json
{
  "message": "How do I solve this level?",
  "session_id": "unique_session_id",
  "current_level": 0,
  "recent_commands": ["ls", "cat readme"],
  "terminal_output": "bandit0@bandit:~$ cat readme\nboJ9jbbUNNfktd78OOpsqOltutMc3MY1"
}
```

**Response**
```json
{
  "success": true,
  "response": "It looks like you've already found the password for the next level in the readme file. The password is 'boJ9jbbUNNfktd78OOpsqOltutMc3MY1'.",
  "session_id": "unique_session_id"
}
```

##### POST /api/mentor/clear/{session_id}

Clear conversation history for a session.

**Response**
```json
{
  "success": true,
  "message": "Conversation cleared for session unique_session_id"
}
```

### User Management

#### GET /api/users

Get all users.

**Response**
```json
[
  {
    "id": 1,
    "username": "user1",
    "email": "<EMAIL>"
  },
  {
    "id": 2,
    "username": "user2",
    "email": "<EMAIL>"
  }
]
```

#### POST /api/users

Create a new user.

**Request Body**
```json
{
  "username": "newuser",
  "email": "<EMAIL>"
}
```

**Response**
```json
{
  "id": 3,
  "username": "newuser",
  "email": "<EMAIL>"
}
```

#### GET /api/users/{user_id}

Get a specific user.

**Response**
```json
{
  "id": 1,
  "username": "user1",
  "email": "<EMAIL>"
}
```

#### PUT /api/users/{user_id}

Update a user.

**Request Body**
```json
{
  "username": "updateduser",
  "email": "<EMAIL>"
}
```

**Response**
```json
{
  "id": 1,
  "username": "updateduser",
  "email": "<EMAIL>"
}
```

#### DELETE /api/users/{user_id}

Delete a user.

**Response**
```
204 No Content
```

## Error Responses

All error responses follow the same format:

```json
{
  "success": false,
  "error": "Error message describing what went wrong"
}
```

### Common HTTP Status Codes

- `200 OK`: Request was successful
- `201 Created`: Resource was created successfully
- `204 No Content`: Request was successful, no content to return
- `400 Bad Request`: Invalid request parameters or body
- `401 Unauthorized`: Authentication required or invalid credentials
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server-side error
