# Bandit Wargame GUI - Configuration Guide

This document provides detailed information about configuring the Bandit Wargame GUI application, including environment variables, settings, and deployment options.

## Table of Contents

- [Environment Variables](#environment-variables)
- [Configuration Files](#configuration-files)
- [Backend Configuration](#backend-configuration)
- [Frontend Configuration](#frontend-configuration)
- [Database Configuration](#database-configuration)
- [Logging Configuration](#logging-configuration)
- [Security Configuration](#security-configuration)
- [Deployment Options](#deployment-options)
- [Troubleshooting](#troubleshooting)

## Environment Variables

The application uses environment variables for configuration. You can set these in a `.env` file in the repository root (next to README.md) or in your deployment environment.

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `FLASK_APP` | Path to the Flask application | `src/main.py` |
| `FLASK_ENV` | Flask environment (`development`, `production`, `testing`) | `development` |
| `SECRET_KEY` | Secret key for session management | `your-secret-key-here` |
| `OPENAI_API_KEY` | API key for OpenAI integration | `sk-...` |
| `OPENAI_API_BASE` | Base URL for OpenAI API | `https://api.openai.com/v1` |
| `OPENAI_API_MODEL` | Model to use for AI mentor | `gpt-3.5-turbo` |
| `DATABASE_URL` | Database connection URL | `sqlite:///app.db` |

### Optional Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Port for the backend server | `5001` |
| `FRONTEND_URL` | URL of the frontend for CORS | `http://localhost:5173` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `MAX_SSH_SESSIONS` | Maximum number of concurrent SSH sessions | `10` |
| `SSH_TIMEOUT` | SSH connection timeout in seconds | `30` |
| `SESSION_LIFETIME` | Session lifetime in seconds | `86400` (24 hours) |
| `RATE_LIMIT` | Maximum requests per minute | `100` |

## Configuration Files

### .env.sample

A sample configuration file is provided at the root of the repository. Copy this file to `.env` in the repository root and update the values as needed:

```bash
# Flask Configuration
FLASK_APP=src/main.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database
DATABASE_URL=sqlite:///app.db

# OpenAI Integration
OPENAI_API_KEY=your-openai-api-key
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_MODEL=gpt-3.5-turbo

# Server Configuration
PORT=5001
FRONTEND_URL=http://localhost:5173

# Logging
LOG_LEVEL=INFO

# SSH Configuration
MAX_SSH_SESSIONS=10
SSH_TIMEOUT=30

# Security
SESSION_LIFETIME=86400
RATE_LIMIT=100
```

## Backend Configuration

The backend is a Flask application with the following configuration options:

### Application Factory

The application is created using the application factory pattern in `src/main.py`:

```python
def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    socketio.init_app(app, cors_allowed_origins=app.config['CORS_ORIGINS'])
    
    # Register blueprints
    from src.routes import api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # Initialize socketio events
    from src.routes.ssh import init_socketio
    init_socketio(socketio)
    
    return app
```

### Configuration Classes

Configuration is handled through Python classes in `src/config.py`:

```python
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-key-please-change'
    DEBUG = False
    TESTING = False
    
    # Database
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///app.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # OpenAI
    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
    OPENAI_API_BASE = os.environ.get('OPENAI_API_BASE', 'https://api.openai.com/v1')
    OPENAI_API_MODEL = os.environ.get('OPENAI_API_MODEL', 'gpt-3.5-turbo')
    
    # Server
    PORT = int(os.environ.get('PORT', 5001))
    HOST = os.environ.get('HOST', '0.0.0.0')
    
    # CORS
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', 'http://localhost:5173').split(',')
    
    # SSH
    MAX_SSH_SESSIONS = int(os.environ.get('MAX_SSH_SESSIONS', 10))
    SSH_TIMEOUT = int(os.environ.get('SSH_TIMEOUT', 30))
    
    # Security
    SESSION_LIFETIME = int(os.environ.get('SESSION_LIFETIME', 86400))  # 24 hours
    RATE_LIMIT = int(os.environ.get('RATE_LIMIT', 100))  # requests per minute


class DevelopmentConfig(Config):
    DEBUG = True
    SQLALCHEMY_ECHO = True


class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False


class ProductionConfig(Config):
    DEBUG = False
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'


config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
```

## Frontend Configuration

The frontend is a React application with Vite. Configuration is handled through:

### Environment Variables

Create a `.env` file in the `frontend/bandit-frontend` directory:

```
VITE_API_BASE_URL=http://localhost:5001/api
VITE_WS_URL=ws://localhost:5001
VITE_APP_NAME=Bandit Wargame GUI
VITE_APP_VERSION=1.0.0
```

### Vite Configuration

`vite.config.js` in the frontend directory:

```javascript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:5001',
        changeOrigin: true,
        secure: false,
      },
    },
  },
});
```

## Database Configuration

The application uses SQLAlchemy with SQLite by default, but can be configured to use other databases:

### SQLite (Default)

```env
DATABASE_URL=sqlite:///app.db
```

### PostgreSQL

```env
DATABASE_URL=postgresql://user:password@localhost:5432/bandit_db
```

### MySQL

```env
DATABASE_URL=mysql+pymysql://user:password@localhost:3306/bandit_db
```

## Logging Configuration

Logging is configured in `src/__init__.py`:

```python
import logging
from logging.handlers import RotatingFileHandler
import os

def configure_logging(app):
    # Create logs directory if it doesn't exist
    if not os.path.exists('logs'):
        os.mkdir('logs')
    
    # Set log level from environment variable or default to INFO
    log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
    numeric_level = getattr(logging, log_level, logging.INFO)
    
    # Configure file handler
    file_handler = RotatingFileHandler(
        'logs/bandit_gui.log',
        maxBytes=10240,
        backupCount=10
    )
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(numeric_level)
    
    # Configure console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    console_handler.setFormatter(logging.Formatter(
        '%(levelname)s: %(message)s'
    )
    
    # Add handlers to the root logger
    app.logger.addHandler(file_handler)
    app.logger.addHandler(console_handler)
    app.logger.setLevel(numeric_level)
    
    app.logger.info('Bandit Wargame GUI startup')
```

## Security Configuration

### CORS Configuration

CORS is configured in the Flask application to allow requests from the frontend:

```python
from flask_cors import CORS

# In create_app()
CORS(app, resources={
    r"/api/*": {
        "origins": app.config['CORS_ORIGINS'],
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization"]
    }
})
```

### Rate Limiting

Rate limiting is implemented using Flask-Limiter:

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app=app,
    key_func=get_remote_address,
    default_limits=[app.config['RATE_LIMIT'] + " per minute"]
)
```

## Deployment Options

### Development

1. **Backend**:
   ```bash
   cd backend/bandit-backend
   python -m venv venv
   source venv/bin/activate  # On Windows: .\venv\Scripts\activate
   pip install -r requirements.txt
   python src/main.py
   ```

2. **Frontend**:
   ```bash
   cd frontend/bandit-frontend
   pnpm install
   pnpm run dev
   ```

### Production with Gunicorn and Nginx

1. **Install Gunicorn**:
   ```bash
   pip install gunicorn
   ```

2. **Run with Gunicorn**:
   ```bash
   gunicorn --bind 0.0.0.0:5001 "src.main:create_app()"
   ```

3. **Nginx Configuration** (`/etc/nginx/sites-available/bandit-gui`):
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://127.0.0.1:5001;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }

       location /socket.io {
           proxy_pass http://127.0.0.1:5001/socket.io;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       }
   }
   ```

4. **Enable the site**:
   ```bash
   sudo ln -s /etc/nginx/sites-available/bandit-gui /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

### Docker Deployment

1. **Dockerfile**:
   ```dockerfile
   FROM python:3.11-slim

   WORKDIR /app
   
   # Install system dependencies
   RUN apt-get update && apt-get install -y \
       gcc \
       python3-dev \
       libffi-dev \
       && rm -rf /var/lib/apt/lists/*
   
   # Install Python dependencies
   COPY backend/bandit-backend/requirements.txt .
   RUN pip install --no-cache-dir -r requirements.txt
   
   # Copy application code
   COPY . .
   
   # Set environment variables
   ENV FLASK_APP=src/main.py
   ENV FLASK_ENV=production
   
   # Expose port
   EXPOSE 5001
   
   # Run the application
   CMD ["gunicorn", "--bind", "0.0.0.0:5001", "src.main:create_app()"]
   ```

2. **docker-compose.yml**:
   ```yaml
   version: '3.8'
   
   services:
     app:
       build: .
       ports:
         - "5001:5001"
       environment:
         - FLASK_ENV=production
         - SECRET_KEY=your-secret-key
         - DATABASE_URL=sqlite:////data/bandit.db
         - OPENAI_API_KEY=your-openai-key
       volumes:
         - ./data:/data
   ```

## Troubleshooting

### Common Issues

1. **Port already in use**:
   ```bash
   lsof -i :5001  # Find process using port 5001
   kill -9 <PID>  # Replace <PID> with the process ID
   ```

2. **Database connection issues**:
   - Verify the database URL in the `.env` file
   - Ensure the database server is running
   - Check database user permissions

3. **CORS errors**:
   - Verify the `FRONTEND_URL` in the backend configuration
   - Ensure the frontend URL is in the `CORS_ORIGINS` list

4. **SSH connection issues**:
   - Verify the SSH server is running and accessible
   - Check firewall settings
   - Ensure proper credentials are being used

### Viewing Logs

- **Backend logs**: `tail -f logs/bandit_gui.log`
- **Nginx logs**: `tail -f /var/log/nginx/error.log`
- **Gunicorn logs**: Check the output of the Gunicorn process

### Getting Help

If you encounter issues not covered in this guide, please:
1. Check the [GitHub Issues](https://github.com/yourusername/bandit-wargame-gui/issues) page
2. Search the [Discussions](https://github.com/yourusername/bandit-wargame-gui/discussions) for similar issues
3. Open a new issue with detailed information about your problem
