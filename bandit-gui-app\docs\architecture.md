# Bandit Wargame GUI Application Architecture

## Overview
A web-based application that provides a simplified interface for playing the Overthewire Bandit wargame, featuring:
- SSH terminal interface
- Level information display
- AI mentor for interactive learning assistance

## Technology Stack

### Backend
- **Framework**: Flask (Python)
- **SSH Client**: Paramiko library for SSH connections
- **WebSocket**: Flask-SocketIO for real-time terminal communication
- **LLM Integration**: OpenAI API for AI mentor functionality
- **Web Scraping**: BeautifulSoup for level data extraction

### Frontend
- **Framework**: React.js
- **Terminal Emulator**: xterm.js for terminal display
- **WebSocket Client**: socket.io-client for real-time communication
- **UI Framework**: Tailwind CSS for styling
- **State Management**: React hooks (useState, useEffect)

## Architecture Components

### 1. SSH Terminal Interface
- **Backend SSH Proxy**: Flask-SocketIO server that maintains SSH connections to bandit.labs.overthewire.org:2220
- **Frontend Terminal**: xterm.js terminal emulator that displays command output and accepts user input
- **Communication**: WebSocket connection between frontend and backend for real-time terminal interaction

### 2. Level Data Display
- **Data Source**: Web scraping from https://overthewire.org/wargames/bandit
- **Storage**: In-memory cache or JSON files for level objectives, commands, and learning materials
- **Display**: React component showing current level information, objectives, and recommended commands

### 3. AI Mentor
- **LLM Backend**: OpenAI API integration for generating contextual help
- **Prompt Engineering**: System prompts that ensure mentor provides guidance without giving direct solutions
- **Chat Interface**: React-based chat component for mentor interaction
- **Context Awareness**: Tracks current level and recent commands to provide relevant assistance

## Application Flow

1. **User Authentication**: Connect to SSH server with bandit credentials
2. **Level Detection**: Parse current directory/prompt to determine active level
3. **Information Display**: Show level objectives and recommended commands
4. **Terminal Interaction**: Execute commands through SSH proxy
5. **Mentor Assistance**: Provide contextual help based on user actions and current level
6. **Progress Tracking**: Monitor level completion and guide to next level

## Security Considerations

- SSH credentials are handled securely through the backend
- No direct SSH access from frontend
- LLM prompts are sanitized to prevent injection attacks
- WebSocket connections are properly authenticated

## Deployment Architecture

- **Development**: Local Flask server with React development server
- **Production**: Containerized deployment with nginx reverse proxy
- **Static Assets**: React build served through Flask static files

