"""
Health Checks for Bandit Wargame GUI
"""
import os
import sys
import time
import json
import socket
import platform
import subprocess
import requests
import psutil
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any, Union
from pathlib import Path

class HealthChecker:
    """Class to perform health checks on the Bandit Wargame GUI application"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the health checker with configuration"""
        self.config = config or self.load_default_config()
        self.results = {
            "timestamp": datetime.utcnow().isoformat(),
            "system": {},
            "application": {},
            "dependencies": {},
            "checks": [],
            "summary": {
                "total": 0,
                "healthy": 0,
                "warning": 0,
                "critical": 0
            }
        }
    
    @staticmethod
    def load_default_config() -> Dict[str, Any]:
        """Load default configuration"""
        return {
            "app": {
                "name": "bandit-wargame-gui",
                "process_name": "python",
                "expected_ports": [5000, 3000],
                "expected_services": ["redis", "postgresql"],
                "log_file": "logs/app.log",
                "max_log_size_mb": 100,
                "max_log_age_days": 7
            },
            "system": {
                "min_disk_space_gb": 5,
                "min_memory_percent": 10,
                "max_cpu_percent": 90,
                "critical_processes": ["ssh", "nginx", "systemd"]
            },
            "endpoints": [
                {"name": "api_health", "url": "http://localhost:5000/health", "method": "GET", "timeout": 5},
                {"name": "frontend", "url": "http://localhost:3000", "method": "GET", "timeout": 5}
            ]
        }
    
    def add_check_result(self, name: str, status: str, message: str, details: str = ""):
        """Add a check result to the results"""
        check = {
            "name": name,
            "status": status.upper(),
            "message": message,
            "details": details,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.results["checks"].append(check)
        self.results["summary"]["total"] += 1
        
        if status.lower() == "healthy":
            self.results["summary"]["healthy"] += 1
        elif status.lower() == "warning":
            self.results["summary"]["warning"] += 1
        else:  # critical
            self.results["summary"]["critical"] += 1
    
    def check_system_resources(self):
        """Check system resources (CPU, memory, disk, etc.)"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_status = "healthy" if cpu_percent < self.config["system"]["max_cpu_percent"] else "critical"
            self.add_check_result(
                "CPU Usage",
                cpu_status,
                f"CPU usage: {cpu_percent:.1f}%",
                f"Threshold: <{self.config['system']['max_cpu_percent']}%"
            )
            
            # Memory usage
            mem = psutil.virtual_memory()
            mem_percent = mem.percent
            mem_available_gb = mem.available / (1024 ** 3)  # Convert to GB
            mem_status = "healthy" if mem_available_gb > self.config["system"]["min_memory_percent"] * mem.total / (100 * (1024 ** 3)) else "critical"
            self.add_check_result(
                "Memory Usage",
                mem_status,
                f"Memory usage: {mem_percent:.1f}% ({mem_available_gb:.1f}GB available)",
                f"Available: {mem_available_gb:.1f}GB, Used: {mem.used / (1024**3):.1f}GB, Total: {mem.total / (1024**3):.1f}GB"
            )
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free_gb = disk.free / (1024 ** 3)
            disk_status = "healthy" if disk_free_gb > self.config["system"]["min_disk_space_gb"] else "critical"
            self.add_check_result(
                "Disk Space",
                disk_status,
                f"Disk usage: {disk_percent:.1f}% ({disk_free_gb:.1f}GB free)",
                f"Free: {disk_free_gb:.1f}GB, Used: {disk.used / (1024**3):.1f}GB, Total: {disk.total / (1024**3):.1f}GB"
            )
            
            # System load
            load_avg = os.getloadavg()
            load_status = "healthy" if load_avg[0] < psutil.cpu_count() * 0.7 else "warning"
            self.add_check_result(
                "System Load",
                load_status,
                f"Load average: {load_avg[0]:.2f}, {load_avg[1]:.2f}, {load_avg[2]:.2f}",
                f"1/5/15 minute load averages"
            )
            
            # System uptime
            uptime_seconds = time.time() - psutil.boot_time()
            uptime_days = uptime_seconds / (24 * 3600)
            self.add_check_result(
                "System Uptime",
                "healthy",
                f"System uptime: {uptime_days:.1f} days",
                f"Last boot: {datetime.fromtimestamp(psutil.boot_time()).isoformat()}"
            )
            
        except Exception as e:
            self.add_check_result(
                "System Resources",
                "critical",
                f"Failed to check system resources: {str(e)}",
                str(e)
            )
    
    def check_application_processes(self):
        """Check if the application processes are running"""
        try:
            # Check main application process
            app_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if self.config["app"]["process_name"] in ' '.join(proc.info['cmdline'] or []):
                        app_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass
            
            if app_processes:
                pids = ", ".join(str(p.pid) for p in app_processes)
                self.add_check_result(
                    "Application Process",
                    "healthy",
                    f"Application is running (PIDs: {pids})",
                    f"Found {len(app_processes)} processes"
                )
            else:
                self.add_check_result(
                    "Application Process",
                    "critical",
                    "Application process is not running",
                    f"Expected process name: {self.config['app']['process_name']}"
                )
            
            # Check critical system processes
            for proc_name in self.config["system"].get("critical_processes", []):
                try:
                    running = any(proc_name in p.info['name'] for p in psutil.process_iter(['name']))
                    status = "healthy" if running else "critical"
                    self.add_check_result(
                        f"Process: {proc_name}",
                        status,
                        f"Process {'is running' if running else 'is not running'}",
                        f"Process name: {proc_name}"
                    )
                except Exception as e:
                    self.add_check_result(
                        f"Process: {proc_name}",
                        "warning",
                        f"Failed to check process: {str(e)}",
                        str(e)
                    )
            
        except Exception as e:
            self.add_check_result(
                "Application Processes",
                "critical",
                f"Failed to check application processes: {str(e)}",
                str(e)
            )
    
    def check_ports(self):
        """Check if required ports are listening"""
        for port in self.config["app"].get("expected_ports", []):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('127.0.0.1', port))
                sock.close()
                
                if result == 0:
                    self.add_check_result(
                        f"Port {port}",
                        "healthy",
                        f"Port {port} is listening",
                        f"Protocol: TCP, Status: Open"
                    )
                else:
                    self.add_check_result(
                        f"Port {port}",
                        "critical",
                        f"Port {port} is not listening",
                        f"Protocol: TCP, Status: Closed"
                    )
            except Exception as e:
                self.add_check_result(
                    f"Port {port}",
                    "warning",
                    f"Failed to check port {port}",
                    str(e)
                )
    
    def check_endpoints(self):
        """Check HTTP endpoints"""
        for endpoint in self.config.get("endpoints", []):
            try:
                start_time = time.time()
                response = requests.request(
                    method=endpoint.get("method", "GET"),
                    url=endpoint["url"],
                    timeout=endpoint.get("timeout", 5)
                )
                response_time = (time.time() - start_time) * 1000  # in ms
                
                # Check if status code is in 2xx range
                status = "healthy" if 200 <= response.status_code < 300 else "warning"
                
                self.add_check_result(
                    f"Endpoint: {endpoint['name']}",
                    status,
                    f"{endpoint['url']} - {response.status_code} ({response_time:.1f}ms)",
                    f"Status: {response.status_code}, Time: {response_time:.1f}ms"
                )
                
            except requests.exceptions.RequestException as e:
                self.add_check_result(
                    f"Endpoint: {endpoint['name']}",
                    "critical",
                    f"Failed to connect to {endpoint['url']}",
                    str(e)
                )
            except Exception as e:
                self.add_check_result(
                    f"Endpoint: {endpoint['name']}",
                    "critical",
                    f"Error checking {endpoint['url']}",
                    str(e)
                )
    
    def check_logs(self):
        """Check application logs for errors"""
        log_file = self.config["app"].get("log_file")
        if not log_file or not os.path.exists(log_file):
            self.add_check_result(
                "Log File",
                "warning",
                f"Log file not found: {log_file}",
                "Log monitoring is disabled"
            )
            return
        
        try:
            # Check log file size
            log_size_mb = os.path.getsize(log_file) / (1024 * 1024)
            max_size_mb = self.config["app"].get("max_log_size_mb", 100)
            
            log_size_status = "healthy"
            if log_size_mb > max_size_mb * 0.9:  # Warning at 90% of max size
                log_size_status = "warning"
            
            self.add_check_result(
                "Log File Size",
                log_size_status,
                f"Log file size: {log_size_mb:.2f}MB",
                f"Max size: {max_size_mb}MB"
            )
            
            # Check log file age
            log_mtime = os.path.getmtime(log_file)
            log_age_days = (time.time() - log_mtime) / (24 * 3600)
            max_age_days = self.config["app"].get("max_log_age_days", 7)
            
            log_age_status = "healthy"
            if log_age_days > max_age_days:
                log_age_status = "warning"
            
            self.add_check_result(
                "Log File Age",
                log_age_status,
                f"Log file age: {log_age_days:.1f} days",
                f"Last modified: {datetime.fromtimestamp(log_mtime).isoformat()}"
            )
            
            # Check for recent errors in logs (last 100 lines)
            try:
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()[-100:]  # Check last 100 lines
                    
                error_lines = [line.strip() for line in lines if any(
                    err in line.lower() for err in ['error', 'exception', 'critical', 'fatal']
                )]
                
                if error_lines:
                    self.add_check_result(
                        "Log Errors",
                        "warning" if len(error_lines) < 5 else "critical",
                        f"Found {len(error_lines)} error(s) in recent logs",
                        "Recent errors:\n" + "\n".join(error_lines[-3:])  # Show last 3 errors
                    )
                else:
                    self.add_check_result(
                        "Log Errors",
                        "healthy",
                        "No recent errors found in logs",
                        f"Checked last {len(lines)} lines"
                    )
                    
            except Exception as e:
                self.add_check_result(
                    "Log Analysis",
                    "warning",
                    f"Failed to analyze log file: {str(e)}",
                    str(e)
                )
                
        except Exception as e:
            self.add_check_result(
                "Log Check",
                "warning",
                f"Failed to check log file: {str(e)}",
                str(e)
            )
    
    def check_dependencies(self):
        """Check application dependencies"""
        try:
            # Check Python version
            python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
            self.add_check_result(
                "Python Version",
                "healthy",
                f"Python {python_version}",
                f"Platform: {platform.platform()}"
            )
            
            # Check installed packages
            try:
                import pkg_resources
                
                required_packages = [
                    'flask',
                    'flask-socketio',
                    'python-dotenv',
                    'paramiko',
                    'openai',
                    'requests',
                    'beautifulsoup4',
                    'sqlalchemy',
                    'psutil',
                    'pytest'
                ]
                
                missing_packages = []
                outdated_packages = []
                
                for package in required_packages:
                    try:
                        dist = pkg_resources.get_distribution(package)
                        self.add_check_result(
                            f"Package: {package}",
                            "healthy",
                            f"{package} {dist.version} is installed",
                            f"Location: {dist.location}"
                        )
                    except pkg_resources.DistributionNotFound:
                        missing_packages.append(package)
                        self.add_check_result(
                            f"Package: {package}",
                            "critical",
                            f"{package} is not installed",
                            "Install with 'pip install -r requirements.txt'"
                        )
                
                if missing_packages:
                    self.add_check_result(
                        "Dependencies",
                        "critical",
                        f"Missing {len(missing_packages)} required package(s)",
                        f"Missing: {', '.join(missing_packages)}"
                    )
                
            except ImportError:
                self.add_check_result(
                    "Python Packages",
                    "warning",
                    "Could not verify installed packages",
                    "Install setuptools for package verification"
                )
            
        except Exception as e:
            self.add_check_result(
                "Dependency Check",
                "warning",
                f"Error checking dependencies: {str(e)}",
                str(e)
            )
    
    def generate_report(self, output_file: str = None) -> str:
        """Generate a health check report"""
        # Update timestamp
        self.results["timestamp"] = datetime.utcnow().isoformat()
        
        # Generate report file
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"health_check_{timestamp}.json"
        
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # Generate a simple text summary
        summary = f"""
        Health Check Report
        ===================
        
        Timestamp: {timestamp}
        
        Summary:
        - Total Checks: {total}
        - Healthy: {healthy}
        - Warnings: {warnings}
        - Critical: {critical}
        
        Detailed results have been saved to: {output_file}
        """.format(
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            total=self.results["summary"]["total"],
            healthy=self.results["summary"]["healthy"],
            warnings=self.results["summary"]["warning"],
            critical=self.results["summary"]["critical"],
            output_file=os.path.abspath(output_file)
        )
        
        print(summary)
        
        # Print critical issues
        critical_issues = [c for c in self.results["checks"] if c["status"] == "CRITICAL"]
        if critical_issues:
            print("\nCRITICAL ISSUES:")
            for issue in critical_issues:
                print(f"- {issue['name']}: {issue['message']}")
        
        # Print warnings
        warnings = [c for c in self.results["checks"] if c["status"] == "WARNING"]
        if warnings:
            print("\nWARNINGS:")
            for warning in warnings:
                print(f"- {warning['name']}: {warning['message']}")
        
        return output_file
    
    def run_all_checks(self) -> bool:
        """Run all health checks"""
        print("Running health checks...\n")
        
        checks = [
            ("System Resources", self.check_system_resources),
            ("Application Processes", self.check_application_processes),
            ("Network Ports", self.check_ports),
            ("HTTP Endpoints", self.check_endpoints),
            ("Log Files", self.check_logs),
            ("Dependencies", self.check_dependencies)
        ]
        
        for name, check in checks:
            print(f"Running check: {name}...")
            try:
                check()
            except Exception as e:
                self.add_check_result(
                    name,
                    "critical",
                    f"Error during check: {str(e)}",
                    str(e)
                )
        
        print("\nHealth checks completed!")
        
        # Generate report
        report_file = self.generate_report()
        
        # Return True if no critical issues
        return self.results["summary"]["critical"] == 0

def main():
    """Main function to run the health checker"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Run health checks for Bandit Wargame GUI')
    parser.add_argument('--config', type=str, default=None,
                        help='Path to configuration file (JSON)')
    parser.add_argument('--output', type=str, default=None,
                        help='Output file for the health check report')
    
    args = parser.parse_args()
    
    # Load config if provided
    config = None
    if args.config and os.path.exists(args.config):
        try:
            with open(args.config, 'r') as f:
                config = json.load(f)
        except Exception as e:
            print(f"Error loading config file: {str(e)}")
            return 1
    
    # Run health checks
    checker = HealthChecker(config=config)
    success = checker.run_all_checks()
    
    # Exit with appropriate status code
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
