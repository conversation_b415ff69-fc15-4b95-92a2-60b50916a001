"""
Test Runner for Bandit Wargame GUI
"""
import os
import sys
import subprocess
import argparse
import json
import time
from typing import List, Dict, Any, Optional
from pathlib import Path

class TestRunner:
    """Test runner for Bandit Wargame GUI"""
    
    def __init__(self, base_dir: str = None, base_url: str = "http://localhost:5001"):
        self.base_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
        self.base_url = base_url.rstrip('/')
        self.results = {
            "tests": [],
            "summary": {
                "total": 0,
                "passed": 0,
                "failed": 0,
                "skipped": 0,
                "duration": 0.0
            },
            "start_time": time.time(),
            "end_time": None
        }
        
        # Set up paths
        self.backend_dir = os.path.join(self.base_dir, "backend", "bandit-backend")
        self.frontend_dir = os.path.join(self.base_dir, "frontend", "bandit-frontend")
        self.e2e_dir = os.path.join(self.base_dir, "e2e_tests")
        self.reports_dir = os.path.join(self.base_dir, "test-reports")
        
        # Create reports directory if it doesn't exist
        os.makedirs(self.reports_dir, exist_ok=True)
    
    def run_command(self, command: List[str], cwd: str = None, env: Dict[str, str] = None) -> Dict[str, Any]:
        """Run a shell command and return the result"""
        cwd = cwd or self.base_dir
        env = env or os.environ.copy()
        
        print(f"Running: {' '.join(command)} in {cwd}")
        
        start_time = time.time()
        try:
            result = subprocess.run(
                command,
                cwd=cwd,
                env=env,
                capture_output=True,
                text=True,
                check=False
            )
            
            duration = time.time() - start_time
            
            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "duration": duration
            }
            
        except Exception as e:
            duration = time.time() - start_time
            return {
                "success": False,
                "error": str(e),
                "duration": duration
            }
    
    def run_python_tests(self) -> Dict[str, Any]:
        """Run Python backend tests"""
        print("\n=== Running Python Backend Tests ===\n")
        
        test_files = [
            "test_api.py",
            "test_ssh_manager.py",
            "test_level_scraper.py",
            "test_ai_mentor.py"
        ]
        
        results = []
        
        for test_file in test_files:
            test_path = os.path.join(self.backend_dir, test_file)
            if not os.path.exists(test_path):
                print(f"Test file not found: {test_path}")
                continue
                
            print(f"Running {test_file}...")
            result = self.run_command(
                [sys.executable, "-m", "pytest", test_path, "-v", "--json-report"],
                cwd=self.backend_dir
            )
            
            test_result = {
                "test_file": test_file,
                "success": result["success"],
                "duration": result["duration"]
            }
            
            if not result["success"]:
                test_result["error"] = result["stderr"] or "Test failed"
            
            results.append(test_result)
        
        return {
            "type": "python",
            "tests": results,
            "passed": all(r["success"] for r in results),
            "total": len(results),
            "passed_count": sum(1 for r in results if r["success"])
        }
    
    def run_javascript_tests(self) -> Dict[str, Any]:
        """Run JavaScript frontend tests"""
        print("\n=== Running JavaScript Frontend Tests ===\n")
        
        # Install dependencies if node_modules doesn't exist or pnpm-lock.yaml is newer
        if not os.path.exists(os.path.join(self.frontend_dir, "node_modules")) or \
           (os.path.exists(os.path.join(self.frontend_dir, "pnpm-lock.yaml")) and 
            os.path.getmtime(os.path.join(self.frontend_dir, "pnpm-lock.yaml")) > 
            os.path.getmtime(os.path.join(self.frontend_dir, "node_modules"))):
            print("Installing frontend dependencies using pnpm...")
            install_result = self.run_command(
                ["pnpm", "install"],
                cwd=self.frontend_dir
            )
            
            if not install_result["success"]:
                return {
                    "type": "javascript",
                    "tests": [],
                    "passed": False,
                    "error": "Failed to install frontend dependencies with pnpm",
                    "details": install_result["stderr"]
                }
        
        # Run tests using pnpm
        test_result = self.run_command(
            ["pnpm", "test", "--", "--json"],
            cwd=self.frontend_dir
        )
        
        if not test_result["success"]:
            return {
                "type": "javascript",
                "tests": [],
                "passed": False,
                "error": "JavaScript tests failed",
                "details": test_result["stderr"]
            }
        
        try:
            test_data = json.loads(test_result["stdout"])
            
            # Process test results
            passed = test_data["numPassedTests"]
            total = test_data["numTotalTests"]
            
            return {
                "type": "javascript",
                "tests": test_data["testResults"][0]["assertionResults"],
                "passed": test_data["success"],
                "total": total,
                "passed_count": passed,
                "coverage": test_data.get("coverage", {})
            }
            
        except (json.JSONDecodeError, KeyError) as e:
            return {
                "type": "javascript",
                "tests": [],
                "passed": False,
                "error": "Failed to parse test results",
                "details": str(e)
            }
    
    def run_e2e_tests(self) -> Dict[str, Any]:
        """Run end-to-end tests"""
        print("\n=== Running End-to-End Tests ===\n")
        
        # Check if Playwright is installed
        playwright_check = self.run_command(
            ["npx", "playwright", "--version"],
            cwd=self.e2e_dir
        )
        
        if not playwright_check["success"]:
            # Install Playwright
            print("Installing Playwright...")
            install_result = self.run_command(
                ["npx", "playwright", "install"],
                cwd=self.e2e_dir
            )
            
            if not install_result["success"]:
                return {
                    "type": "e2e",
                    "tests": [],
                    "passed": False,
                    "error": "Failed to install Playwright",
                    "details": install_result["stderr"]
                }
        
        # Run Playwright tests
        test_result = self.run_command(
            ["npx", "playwright", "test", "--reporter=json"],
            cwd=self.e2e_dir
        )
        
        if not test_result["success"]:
            return {
                "type": "e2e",
                "tests": [],
                "passed": False,
                "error": "End-to-end tests failed",
                "details": test_result["stderr"]
            }
        
        try:
            test_data = json.loads(test_result["stdout"])
            
            # Process test results
            passed = sum(1 for suite in test_data["suites"] for test in suite["specs"] if test["ok"]) if "suites" in test_data else 0
            total = sum(1 for suite in test_data["suites"] for _ in suite["specs"]) if "suites" in test_data else 0
            
            return {
                "type": "e2e",
                "tests": [test for suite in test_data.get("suites", []) for test in suite.get("specs", [])],
                "passed": passed == total,
                "total": total,
                "passed_count": passed
            }
            
        except (json.JSONDecodeError, KeyError) as e:
            return {
                "type": "e2e",
                "tests": [],
                "passed": False,
                "error": "Failed to parse test results",
                "details": str(e)
            }
    
    def run_security_audit(self) -> Dict[str, Any]:
        """Run security audit"""
        print("\n=== Running Security Audit ===\n")
        
        from security_audit import SecurityAudit
        audit = SecurityAudit(base_url=self.base_url)
        
        security_script = os.path.join(self.base_dir, "security_audit.py")
        
        if not os.path.exists(security_script):
            return {
                "type": "security",
                "tests": [],
                "passed": False,
                "error": "Security audit script not found"
            }
        
        result = self.run_command(
            [sys.executable, security_script],
            cwd=self.base_dir
        )
        
        if not result["success"]:
            return {
                "type": "security",
                "tests": [],
                "passed": False,
                "error": "Security audit failed",
                "details": result["stderr"]
            }
        
        try:
            # Try to parse the security report
            report_path = os.path.join(self.base_dir, "security_audit_report.json")
            with open(report_path, 'r') as f:
                security_report = json.load(f)
            
            # Combine all test results into a single tests array
            tests = []
            
            # Add security issues as failed tests
            for issue in security_report.get("security_issues", []):
                tests.append({
                    "name": f"Security: {issue['title']}",
                    "status": "failed",
                    "severity": issue.get("severity", "high"),
                    "details": issue.get("description", ""),
                    "recommendation": issue.get("recommendation", "")
                })
            
            # Add warnings as passed tests with warning status
            for warning in security_report.get("warnings", []):
                tests.append({
                    "name": f"Warning: {warning['title']}",
                    "status": "warning",
                    "details": warning.get("description", ""),
                    "recommendation": warning.get("recommendation", "")
                })
            
            # Add passed checks
            for check in security_report.get("passed_checks", []):
                tests.append({
                    "name": check.get("title", "Security Check"),
                    "status": "passed"
                })
            
            # Determine if the audit passed (no security issues)
            passed = security_report["summary"].get("issues_found", 0) == 0
            
            return {
                "type": "security",
                "tests": tests,
                "passed": passed,
                "total": security_report["summary"].get("total_checks", 0),
                "passed_count": security_report["summary"].get("passed", 0),
                "warnings": security_report["summary"].get("warnings", 0),
                "issues_found": security_report["summary"].get("issues_found", 0)
            }
            
        except Exception as e:
            return {
                "type": "security",
                "tests": [],
                "passed": False,
                "error": "Failed to parse security report",
                "details": str(e)
            }
    
    def run_performance_tests(self) -> Dict[str, Any]:
        """Run performance tests"""
        print("\n=== Running Performance Tests ===\n")
        
        performance_script = os.path.join(self.base_dir, "performance_tests", "load_testing.py")
        
        if not os.path.exists(performance_script):
            return {
                "type": "performance",
                "tests": [],
                "passed": False,
                "error": "Performance test script not found"
            }
        
        result = self.run_command(
            [sys.executable, performance_script],
            cwd=os.path.dirname(performance_script)
        )
        
        if not result["success"]:
            return {
                "type": "performance",
                "tests": [],
                "passed": False,
                "error": "Performance tests failed",
                "details": result["stderr"]
            }
        
        try:
            # Try to parse the performance report
            report_path = os.path.join(self.base_dir, "performance_report.html")
            
            return {
                "type": "performance",
                "tests": [{"name": "Load Test"}],
                "passed": True,
                "report": report_path
            }
            
        except Exception as e:
            return {
                "type": "performance",
                "tests": [],
                "passed": False,
                "error": "Failed to process performance report",
                "details": str(e)
            }
    
    def run_deployment_validation(self) -> Dict[str, Any]:
        """Run deployment validation"""
        print("\n=== Running Deployment Validation ===\n")
        
        from deployment_tests.production_validation import DeploymentValidator, main as run_deployment_validation
        validator = DeploymentValidator(base_url=self.base_url)
        
        validation_script = os.path.join(self.base_dir, "deployment_tests", "production_validation.py")
        
        if not os.path.exists(validation_script):
            return {
                "type": "deployment",
                "tests": [],
                "passed": False,
                "error": "Deployment validation script not found"
            }
        
        result = self.run_command(
            [sys.executable, validation_script],
            cwd=os.path.dirname(validation_script)
        )
        
        if not result["success"]:
            return {
                "type": "deployment",
                "tests": [],
                "passed": False,
                "error": "Deployment validation failed",
                "details": result["stderr"]
            }
        
        try:
            # Try to parse the validation report
            report_path = os.path.join(self.base_dir, "deployment_validation_report.json")
            with open(report_path, 'r') as f:
                validation_report = json.load(f)
            
            passed = validation_report["summary"]["failed"] == 0
            
            return {
                "type": "deployment",
                "tests": validation_report.get("checks", []),
                "passed": passed,
                "total": validation_report["summary"]["total_checks"],
                "passed_count": validation_report["summary"]["passed"],
                "warnings": validation_report["summary"]["warnings"]
            }
            
        except Exception as e:
            return {
                "type": "deployment",
                "tests": [],
                "passed": False,
                "error": "Failed to parse deployment validation report",
                "details": str(e)
            }
    
    def generate_report(self, output_file: str = None) -> str:
        """Generate a test report"""
        self.results["end_time"] = time.time()
        self.results["summary"]["duration"] = self.results["end_time"] - self.results["start_time"]
        
        # Calculate summary
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for test in self.results["tests"]:
            total_tests += test.get("total", 0)
            passed_tests += test.get("passed_count", 0)
            failed_tests += test.get("total", 0) - test.get("passed_count", 0)
        
        self.results["summary"]["total"] = total_tests
        self.results["summary"]["passed"] = passed_tests
        self.results["summary"]["failed"] = failed_tests
        
        # Generate report file
        if not output_file:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.reports_dir, f"test_report_{timestamp}.json")
        
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # Generate a simple text summary
        start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(self.results["start_time"]))
        duration = self.results["summary"]["duration"]
        pass_rate = passed_tests / total_tests if total_tests > 0 else 0.0
        
        summary = f"""
        Test Execution Summary
        =====================
        
        Start Time: {start_time}
        Duration: {duration:.2f} seconds
        
        Summary:
        - Total Tests: {total_tests}
        - Passed: {passed_tests}
        - Failed: {failed_tests}
        - Pass Rate: {pass_rate:.1%}
        
        Detailed results have been saved to: {output_file}
        """
        
        print(summary)
        
        # Print failed tests
        failed = [t for t in self.results["tests"] if not t.get("passed", True)]
        if failed:
            print("\nFailed Test Suites:")
            for test in failed:
                print(f"- {test['type'].title()}: {test.get('error', 'Unknown error')}")
        
        return output_file
    
    def run_all_tests(self, test_types: List[str] = None) -> bool:
        """Run all tests"""
        if test_types is None:
            test_types = ["python", "javascript", "e2e", "security", "performance", "deployment"]
        
        test_functions = {
            "python": self.run_python_tests,
            "javascript": self.run_javascript_tests,
            "e2e": self.run_e2e_tests,
            "security": self.run_security_audit,
            "performance": self.run_performance_tests,
            "deployment": self.run_deployment_validation
        }
        
        for test_type in test_types:
            if test_type in test_functions:
                print(f"\n{'='*40}")
                print(f"Running {test_type.upper()} tests...")
                print(f"{'='*40}")
                
                result = test_functions[test_type]()
                self.results["tests"].append(result)
                
                status = "PASSED" if result.get("passed", False) else "FAILED"
                print(f"\n{test_type.upper()} tests {status}")
        
        # Generate report
        report_file = self.generate_report()
        
        # Return True if all tests passed
        return all(test.get("passed", False) for test in self.results["tests"])

def main():
    """Main function to run the test runner"""
    parser = argparse.ArgumentParser(description='Run tests for Bandit Wargame GUI')
    parser.add_argument('--types', type=str, nargs='+',
                        choices=['python', 'javascript', 'e2e', 'security', 'performance', 'deployment', 'all'],
                        default=['all'],
                        help='Types of tests to run (default: all)')
    parser.add_argument('--dir', type=str, default=None,
                        help='Base directory of the project (default: current directory)')
    parser.add_argument('--url', type=str, default="http://localhost:5001",
                      help='Base URL for the application (default: http://localhost:5001)')
    
    args = parser.parse_args()
    
    # Handle 'all' test types
    if 'all' in args.types:
        test_types = ['python', 'javascript', 'e2e', 'security', 'performance', 'deployment']
    else:
        test_types = args.types
    
    # Create test runner instance
    test_runner = TestRunner(base_dir=args.dir, base_url=args.url)
    success = test_runner.run_all_tests(test_types)
    
    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
