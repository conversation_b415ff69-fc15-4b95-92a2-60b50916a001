from flask import Blueprint, request, jsonify
from flask_socketio import emit
from src.ai_mentor import ai_mentor

mentor_bp = Blueprint('mentor', __name__)

def init_mentor_socketio(socketio):
    """Initialize SocketIO events for <PERSON> mentor"""
    
    @socketio.on('mentor_message')
    def handle_mentor_message(data):
        """Handle AI mentor chat message"""
        try:
            user_message = data.get('message', '')
            session_id = data.get('session_id', 'default')
            current_level = data.get('current_level', 0)
            recent_commands = data.get('recent_commands', [])
            terminal_output = data.get('terminal_output', '')
            
            if not user_message.strip():
                emit('mentor_error', {'message': 'Message cannot be empty'})
                return
            
            # Generate AI response
            ai_response = ai_mentor.get_response(
                user_message=user_message,
                session_id=session_id,
                current_level=current_level,
                recent_commands=recent_commands,
                terminal_output=terminal_output
            )
            
            # Send response back to client
            emit('mentor_response', {
                'message': ai_response,
                'session_id': session_id,
                'timestamp': data.get('timestamp')
            })
            
        except Exception as e:
            emit('mentor_error', {'message': f'Error processing message: {str(e)}'})
    
    @socketio.on('mentor_clear')
    def handle_mentor_clear(data):
        """Clear mentor conversation history"""
        try:
            session_id = data.get('session_id', 'default')
            ai_mentor.clear_conversation(session_id)
            
            emit('mentor_cleared', {
                'session_id': session_id,
                'message': 'Conversation history cleared'
            })
            
        except Exception as e:
            emit('mentor_error', {'message': f'Error clearing conversation: {str(e)}'})

@mentor_bp.route('/hint/<int:level_num>', methods=['GET'])
def get_level_hint(level_num):
    """Get a general hint for a specific level"""
    try:
        hint = ai_mentor.get_level_hint(level_num)
        return jsonify({
            'success': True,
            'level': level_num,
            'hint': hint
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@mentor_bp.route('/explain/<command>', methods=['GET'])
def explain_command(command):
    """Get explanation for a specific command"""
    try:
        explanation = ai_mentor.explain_command(command)
        return jsonify({
            'success': True,
            'command': command,
            'explanation': explanation
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@mentor_bp.route('/chat', methods=['POST'])
def chat_with_mentor():
    """REST endpoint for chatting with mentor"""
    try:
        data = request.get_json()
        
        user_message = data.get('message', '')
        session_id = data.get('session_id', 'default')
        current_level = data.get('current_level', 0)
        recent_commands = data.get('recent_commands', [])
        terminal_output = data.get('terminal_output', '')
        
        if not user_message.strip():
            return jsonify({
                'success': False,
                'error': 'Message cannot be empty'
            }), 400
        
        # Generate AI response
        ai_response = ai_mentor.get_response(
            user_message=user_message,
            session_id=session_id,
            current_level=current_level,
            recent_commands=recent_commands,
            terminal_output=terminal_output
        )
        
        return jsonify({
            'success': True,
            'response': ai_response,
            'session_id': session_id
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@mentor_bp.route('/clear/<session_id>', methods=['POST'])
def clear_conversation(session_id):
    """Clear conversation history for a session"""
    try:
        ai_mentor.clear_conversation(session_id)
        return jsonify({
            'success': True,
            'message': f'Conversation cleared for session {session_id}'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

