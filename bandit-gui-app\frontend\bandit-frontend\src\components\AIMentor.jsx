import { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Bot, User, Send, Trash2, Lightbulb, HelpCircle } from 'lucide-react';

const AIMentor = ({ socket, sessionId, currentLevel = 0, recentCommands = [] }) => {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const scrollAreaRef = useRef(null);
  const inputRef = useRef(null);

  useEffect(() => {
    if (!socket) return;

    const handleMentorResponse = (data) => {
      if (data.session_id === sessionId) {
        setMessages(prev => [...prev, {
          id: Date.now(),
          type: 'ai',
          content: data.message,
          timestamp: new Date()
        }]);
        setIsLoading(false);
      }
    };

    const handleMentorError = (data) => {
      setMessages(prev => [...prev, {
        id: Date.now(),
        type: 'error',
        content: data.message,
        timestamp: new Date()
      }]);
      setIsLoading(false);
    };

    const handleMentorCleared = () => {
      setMessages([]);
    };

    socket.on('mentor_response', handleMentorResponse);
    socket.on('mentor_error', handleMentorError);
    socket.on('mentor_cleared', handleMentorCleared);

    return () => {
      socket.off('mentor_response', handleMentorResponse);
      socket.off('mentor_error', handleMentorError);
      socket.off('mentor_cleared', handleMentorCleared);
    };
  }, [socket, sessionId]);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [messages]);

  const sendMessage = () => {
    if (!inputMessage.trim() || !socket || isLoading) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Send to AI mentor via socket
    socket.emit('mentor_message', {
      message: inputMessage,
      session_id: sessionId,
      current_level: currentLevel,
      recent_commands: recentCommands,
      timestamp: Date.now()
    });

    setInputMessage('');
  };

  const clearConversation = () => {
    if (socket) {
      socket.emit('mentor_clear', { session_id: sessionId });
    }
  };

  const getHint = async () => {
    try {
      const response = await fetch(`/api/mentor/hint/${currentLevel}`);
      const data = await response.json();
      
      if (data.success) {
        setMessages(prev => [...prev, {
          id: Date.now(),
          type: 'hint',
          content: data.hint,
          timestamp: new Date()
        }]);
      }
    } catch (error) {
      console.error('Error getting hint:', error);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatMessage = (message) => {
    // Simple formatting for code blocks and commands
    return message.split('`').map((part, index) => {
      if (index % 2 === 1) {
        return <code key={index} className="bg-slate-700 px-1 py-0.5 rounded text-sm font-mono">{part}</code>;
      }
      return part;
    });
  };

  return (
    <Card className="w-full h-full flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bot className="w-5 h-5 text-yellow-400" />
            <span>AI Mentor</span>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="default"
              size="sm"
              onClick={getHint}
              className="text-xs"
            >
              <Lightbulb className="w-3 h-3 mr-1" />
              Hint
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={clearConversation}
              className="text-xs"
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </CardTitle>
        <div className="flex items-center space-x-2">
          <Badge variant="default" className="text-xs">
            Level {currentLevel}
          </Badge>
          {recentCommands.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {recentCommands.length} recent commands
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col space-y-3 p-3">
        {/* Messages Area */}
        <ScrollArea ref={scrollAreaRef} className="flex-1 pr-3">
          <div className="space-y-3">
            {messages.length === 0 && (
              <div className="text-center text-slate-400 text-sm py-8">
                <HelpCircle className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>Ask me anything about the current level!</p>
                <p className="text-xs mt-1">I'll guide you without giving away the solution.</p>
              </div>
            )}
            
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[85%] rounded-lg p-3 text-sm ${
                    message.type === 'user'
                      ? 'bg-yellow-600 text-white'
                      : message.type === 'error'
                      ? 'bg-red-600 text-white'
                      : message.type === 'hint'
                      ? 'bg-yellow-600 text-white'
                      : 'bg-slate-800 text-gray-100'
                  }`}
                >
                  <div className="flex items-start space-x-2">
                    {message.type !== 'user' && (
                      <div className="flex-shrink-0 mt-0.5">
                        {message.type === 'hint' ? (
                          <Lightbulb className="w-3 h-3" />
                        ) : (
                          <Bot className="w-3 h-3" />
                        )}
                      </div>
                    )}
                    <div className="flex-1">
                      <div className="leading-relaxed">
                        {formatMessage(message.content)}
                      </div>
                      <div className="text-xs opacity-70 mt-1">
                        {message.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                    {message.type === 'user' && (
                      <User className="w-3 h-3 flex-shrink-0 mt-0.5" />
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-slate-800 rounded-lg p-3 text-sm">
                  <div className="flex items-center space-x-2">
                    <Bot className="w-3 h-3" />
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                      <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="flex space-x-2">
          <Input
            ref={inputRef}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask for guidance..."
            disabled={isLoading}
            className="flex-1"
          />
          <Button
            onClick={sendMessage}
            disabled={!inputMessage.trim() || isLoading}
            size="sm"
          >
            <Send className="w-4 h-4 text-white" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default AIMentor;

