"""
Security Audit Script for Bandit Wargame GUI
"""
import os
import json
import subprocess
import requests
from urllib.parse import urljoin
from typing import Dict, List, Optional, Any, Tuple
import ssl
import socket
import re

class SecurityAudit:
    def __init__(self, base_url: str = "http://localhost:5001"):
        self.base_url = base_url.rstrip('/')
        self.results = {
            "security_issues": [],
            "warnings": [],
            "passed_checks": [],
            "summary": {
                "total_checks": 0,
                "issues_found": 0,
                "warnings": 0,
                "passed": 0
            }
        }
    
    def add_issue(self, title: str, description: str, severity: str, recommendation: str = ""):
        """Add a security issue to the results"""
        self.results["summary"]["issues_found"] += 1
        self.results["security_issues"].append({
            "title": title,
            "description": description,
            "severity": severity,
            "recommendation": recommendation
        })
    
    def add_warning(self, title: str, description: str, recommendation: str = ""):
        """Add a warning to the results"""
        self.results["summary"]["warnings"] += 1
        self.results["warnings"].append({
            "title": title,
            "description": description,
            "recommendation": recommendation
        })
    
    def add_passed_check(self, title: str):
        """Add a passed check to the results"""
        self.results["summary"]["passed"] += 1
        self.results["passed_checks"].append({
            "title": title,
            "status": "PASSED"
        })
    
    def update_summary(self):
        """Update the summary with total checks"""
        total = (
            self.results["summary"]["issues_found"] +
            self.results["summary"]["warnings"] +
            self.results["summary"]["passed"]
        )
        self.results["summary"]["total_checks"] = total
    
    def check_authentication(self):
        """Check authentication mechanisms"""
        try:
            # Test for default credentials
            response = requests.post(
                f"{self.base_url}/api/ssh/connect",
                json={
                    "hostname": "bandit.labs.overthewire.org",
                    "port": 2220,
                    "username": "admin",
                    "password": "admin"
                },
                timeout=5
            )
            
            if response.status_code == 200:
                self.add_issue(
                    "Default Credentials",
                    "Default credentials (admin/admin) are accepted by the application.",
                    "High",
                    "Implement proper authentication and disable default credentials."
                )
            else:
                self.add_passed_check("Default Credentials Check")
                
        except Exception as e:
            self.add_warning(
                "Authentication Check Failed",
                f"Could not complete authentication check: {str(e)}",
                "Ensure the application is running and accessible."
            )
    
    def check_sql_injection(self):
        """Check for SQL injection vulnerabilities"""
        test_payloads = [
            "' OR '1'='1",
            '"; DROP TABLE users; --',
            "1; SELECT * FROM users"
        ]
        
        vulnerable_endpoints = []
        
        # Test search endpoint
        for payload in test_payloads:
            try:
                response = requests.get(
                    f"{self.base_url}/api/levels/search",
                    params={"q": payload},
                    timeout=5
                )
                
                # Check for error messages that might indicate SQL injection
                if any(error in response.text.lower() for error in ["sql", "syntax", "error"]):
                    vulnerable_endpoints.append("/api/levels/search")
                    break
                    
            except Exception:
                continue
        
        if vulnerable_endpoints:
            self.add_issue(
                "SQL Injection Vulnerability",
                f"Potential SQL injection found in endpoints: {', '.join(vulnerable_endpoints)}",
                "Critical",
                "Use parameterized queries and prepared statements to prevent SQL injection."
            )
        else:
            self.add_passed_check("SQL Injection Check")
    
    def check_xss_vulnerabilities(self):
        """Check for Cross-Site Scripting (XSS) vulnerabilities"""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            '" onmouseover="alert(1)"',
            "javascript:alert('XSS')"
        ]
        
        vulnerable_endpoints = []
        
        # Test chat endpoint
        for payload in xss_payloads:
            try:
                response = requests.post(
                    f"{self.base_url}/api/mentor/chat",
                    json={"message": payload, "session_id": "test-session"},
                    timeout=5
                )
                
                # Check if the payload was reflected in the response
                if payload in response.text and "<script>" in payload:
                    vulnerable_endpoints.append("/api/mentor/chat")
                    break
                    
            except Exception:
                continue
        
        if vulnerable_endpoints:
            self.add_issue(
                "Cross-Site Scripting (XSS) Vulnerability",
                f"Potential XSS vulnerability found in endpoints: {', '.join(vulnerable_endpoints)}",
                "High",
                "Implement proper input validation and output encoding to prevent XSS attacks."
            )
        else:
            self.add_passed_check("XSS Vulnerability Check")
    
    def check_csrf_protection(self):
        """Check for CSRF protection"""
        try:
            # Check if CSRF tokens are being used in forms
            response = requests.get(f"{self.base_url}/")
            
            # Look for CSRF token in meta tags or forms
            csrf_meta = re.search(r'<meta[^>]*name=["\']csrf-token["\']', response.text, re.IGNORECASE)
            csrf_input = re.search(r'<input[^>]*name=["\']_csrf["\']', response.text, re.IGNORECASE)
            
            if not csrf_meta and not csrf_input:
                self.add_issue(
                    "Missing CSRF Protection",
                    "CSRF tokens not detected in the application.",
                    "High",
                    "Implement CSRF tokens for all state-changing operations."
                )
            else:
                self.add_passed_check("CSRF Protection Check")
                
        except Exception as e:
            self.add_warning(
                "CSRF Check Failed",
                f"Could not complete CSRF check: {str(e)}",
                "Ensure the application is running and accessible."
            )
    
    def check_secure_headers(self):
        """Check for security-related HTTP headers"""
        required_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Content-Security-Policy": "default-src 'self'",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains"
        }
        
        missing_headers = []
        
        try:
            response = requests.get(self.base_url, timeout=5)
            
            for header, expected_value in required_headers.items():
                if header not in response.headers:
                    missing_headers.append(header)
                elif expected_value and expected_value.lower() not in response.headers[header].lower():
                    self.add_warning(
                        f"Insecure {header} Configuration",
                        f"{header} is present but has an insecure value: {response.headers[header]}",
                        f"Set {header} to: {expected_value}"
                    )
            
            if missing_headers:
                self.add_issue(
                    "Missing Security Headers",
                    f"The following security headers are missing: {', '.join(missing_headers)}",
                    "Medium",
                    f"Add the missing security headers to enhance the application's security."
                )
            else:
                self.add_passed_check("Security Headers Check")
                
        except Exception as e:
            self.add_warning(
                "Security Headers Check Failed",
                f"Could not check security headers: {str(e)}",
                "Ensure the application is running and accessible."
            )
    
    def check_ssl_tls_configuration(self):
        """Check SSL/TLS configuration"""
        if not self.base_url.startswith('https'):
            self.add_warning(
                "SSL/TLS Not Enforced",
                "The application is not using HTTPS.",
                "Configure the application to use HTTPS and redirect all HTTP traffic to HTTPS."
            )
            return
        
        try:
            hostname = self.base_url.split('//')[1].split('/')[0]
            port = 443
            
            # Check for SSL/TLS versions and ciphers
            context = ssl.create_default_context()
            with socket.create_connection((hostname, port)) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cipher = ssock.cipher()
                    protocol = ssock.version()
                    
                    if protocol in ['SSLv2', 'SSLv3', 'TLSv1', 'TLSv1.1']:
                        self.add_issue(
                            "Insecure SSL/TLS Protocol",
                            f"Insecure protocol version detected: {protocol}",
                            "High",
                            "Disable support for insecure protocols (SSLv2, SSLv3, TLSv1.0, TLSv1.1)."
                        )
                    else:
                        self.add_passed_check("SSL/TLS Protocol Check")
                    
                    # Check for weak ciphers using the negotiated cipher
                    weak_ciphers = [
                        'DES', '3DES', 'RC4', 'MD5', 'SHA1', 'NULL', 'EXPORT', 'ANON', 'ADH', 'LOW', 'PSK', 'SRP', 'CBC'
                    ]
                    
                    # Get the negotiated cipher (cipher[0] is the name of the cipher)
                    negotiated_cipher = cipher[0] if cipher else None
                    
                    if not negotiated_cipher or any(weak in negotiated_cipher for weak in weak_ciphers):
                        self.add_issue(
                            "Weak Cipher Suite",
                            f"The server negotiated a weak cipher suite: {negotiated_cipher or 'No cipher negotiated'}",
                            "High",
                            "Disable weak cipher suites and use strong encryption (e.g., AES-GCM, AES-256)."
                        )
                    else:
                        self.add_passed_check("Cipher Suite Check")
                        
        except Exception as e:
            self.add_warning(
                "SSL/TLS Check Failed",
                f"Could not complete SSL/TLS check: {str(e)}",
                "Ensure the application is running with SSL/TLS enabled and accessible."
            )
    
    def check_dependencies(self):
        """Check for known vulnerabilities in dependencies"""
        try:
            # Check Python dependencies
            result = subprocess.run(
                ['safety', 'check', '--json'],
                capture_output=True,
                text=True,
                check=False
            )
            
            if result.returncode != 0:
                try:
                    vulnerabilities = json.loads(result.stdout)
                    for vuln in vulnerabilities.get('vulnerabilities', []):
                        self.add_issue(
                            f"Vulnerable Dependency: {vuln.get('package_name')}",
                            f"{vuln.get('vulnerability_id')}: {vuln.get('advisory')}",
                            "High" if vuln.get('severity', '').lower() in ['high', 'critical'] else "Medium",
                            f"Update {vuln.get('package_name')} to version {vuln.get('fixed_versions', 'a secure version')}"
                        )
                except json.JSONDecodeError:
                    self.add_warning(
                        "Dependency Check Failed",
                        "Could not parse dependency check results.",
                        "Run 'safety check' manually to check for vulnerable dependencies."
                    )
            else:
                self.add_passed_check("Dependency Vulnerability Check")
                
        except FileNotFoundError:
            self.add_warning(
                "Dependency Check Not Available",
                "The 'safety' tool is not installed.",
                "Install it with 'pip install safety' to check for vulnerable dependencies."
            )
        except Exception as e:
            self.add_warning(
                "Dependency Check Error",
                f"Error checking dependencies: {str(e)}",
                "Check the application's dependencies manually for known vulnerabilities."
            )
    
    def run_all_checks(self):
        """Run all security checks"""
        print("Starting security audit...\n")
        
        checks = [
            ("Authentication Check", self.check_authentication),
            ("SQL Injection Check", self.check_sql_injection),
            ("XSS Vulnerability Check", self.check_xss_vulnerabilities),
            ("CSRF Protection Check", self.check_csrf_protection),
            ("Security Headers Check", self.check_secure_headers),
            ("SSL/TLS Configuration Check", self.check_ssl_tls_configuration),
            ("Dependency Check", self.check_dependencies)
        ]
        
        for name, check in checks:
            print(f"Running {name}...")
            try:
                check()
            except Exception as e:
                self.add_warning(
                    f"{name} Error",
                    f"An error occurred during {name.lower()}: {str(e)}",
                    "Review the application logs for more details."
                )
        
        self.update_summary()
        
        print("\nSecurity audit completed!")
        print(f"\nSummary:")
        print(f"- Total checks: {self.results['summary']['total_checks']}")
        print(f"- Issues found: {self.results['summary']['issues_found']}")
        print(f"- Warnings: {self.results['summary']['warnings']}")
        print(f"- Passed: {self.results['summary']['passed']}")
        
        return self.results

    def generate_report(self, output_file: str = "security_audit_report.json"):
        """Generate a JSON report of the security audit"""
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\nSecurity audit report saved to: {output_file}")

if __name__ == "__main__":
    import argparse
    import os
    
    parser = argparse.ArgumentParser(description='Run a security audit on the Bandit Wargame GUI')
    parser.add_argument('--url', type=str, default="http://localhost:5001",
                      help='Base URL of the application (default: http://localhost:5001)')
    parser.add_argument('--env', action='store_true',
                      help='Load base URL from BANDIT_API_URL environment variable if set')
    parser.add_argument('--output', type=str, default="security_audit_report.json",
                        help='Output file for the audit report (default: security_audit_report.json)')
    
    args = parser.parse_args()
    
    # Get base URL from environment if --env flag is set
    if args.env:
        base_url = os.environ.get('BANDIT_API_URL', args.url)
    else:
        base_url = args.url
    
    # Ensure URL has scheme
    if not base_url.startswith(('http://', 'https://')):
        base_url = f'http://{base_url}'
    
    # Run the security audit
    auditor = SecurityAudit(base_url=base_url)
    auditor.run_all_checks()
    auditor.generate_report(output_file=args.output)
