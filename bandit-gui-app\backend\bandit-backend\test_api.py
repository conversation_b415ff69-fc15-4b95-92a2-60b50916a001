"""
API Test Suite for Bandit Wargame GUI Backend - Flask Test Client Version
"""
import pytest
import json
from unittest.mock import patch, MagicMock
from src.main import create_app

# Create test app and client
app = create_app()
app.config['TESTING'] = True
client = app.test_client()
socketio_client = None

# Setup test client with Socket.IO support
@pytest.fixture(autouse=True)
def setup_socketio():
    global socketio_client
    with app.app_context():
        socketio_client = app.test_client()
        socketio_client.get('/')
        yield
        socketio_client = None

# Fixtures
@pytest.fixture
def mock_ssh_session():
    return {
        "session_id": "test-session-123",
        "hostname": "bandit.labs.overthewire.org",
        "port": 2220,
        "username": "bandit0",
        "level": 0
    }

@pytest.fixture
def mock_level_data():
    return {
        "level": 0,
        "title": "Bandit Level 0",
        "goal": "Find the password for Level 1",
        "commands": ["ls", "cat"],
        "reading_materials": ["man ls", "man cat"]
    }

# SSH API Tests
def test_ssh_connect(mock_ssh_session):
    with patch('src.routes.ssh.SSHManager.create_session') as mock_create:
        mock_create.return_value = "test-session-123"
        
        response = client.post(
            "/api/ssh/connect",
            json={
                "hostname": "bandit.labs.overthewire.org",
                "port": 2220,
                "username": "bandit0",
                "password": "bandit0"
            }
        )
        
        assert response.status_code == 200
        assert "session_id" in response.json()
        assert response.json()["session_id"] == "test-session-123"

def test_ssh_execute_command():
    with patch('src.routes.ssh.SSHManager.execute_command') as mock_execute:
        mock_execute.return_value = "command output"
        
        response = client.post(
            "/api/ssh/command",
            json={
                "session_id": "test-session-123",
                "command": "ls -la"
            }
        )
        
        assert response.status_code == 200
        assert response.json()["output"] == "command output"

def test_ssh_disconnect():
    with patch('src.routes.ssh.SSHManager.close_session') as mock_close:
        mock_close.return_value = True
        
        response = client.post(
            "/api/ssh/disconnect",
            json={"session_id": "test-session-123"}
        )
        
        assert response.status_code == 200
        assert response.json()["status"] == "disconnected"

# Levels API Tests
def test_get_level(mock_level_data):
    with patch('src.routes.levels.LevelManager.get_level') as mock_get_level:
        mock_get_level.return_value = mock_level_data
        
        response = client.get("/api/levels/0")
        
        assert response.status_code == 200
        data = response.json()
        assert data["level"] == 0
        assert "title" in data
        assert "goal" in data

def test_search_levels():
    with patch('src.routes.levels.LevelManager.search_levels') as mock_search:
        mock_search.return_value = [{"level": 0, "title": "Test Level"}]
        
        response = client.get("/api/levels/search?q=test")
        
        assert response.status_code == 200
        assert isinstance(response.json(), list)
        assert len(response.json()) > 0

def test_refresh_levels():
    with patch('src.routes.levels.LevelManager.refresh_levels') as mock_refresh:
        mock_refresh.return_value = True
        
        response = client.post("/api/levels/refresh")
        
        assert response.status_code == 200
        assert response.json()["status"] == "success"

# Mentor API Tests
def test_chat_endpoint():
    with patch('src.routes.mentor.AIMentor.chat') as mock_chat:
        mock_chat.return_value = "Test response from AI"
        
        response = client.post(
            "/api/mentor/chat",
            json={
                "message": "Hello, AI",
                "session_id": "test-session-123"
            }
        )
        
        assert response.status_code == 200
        assert "response" in response.json()

def test_get_hint():
    with patch('src.routes.mentor.AIMentor.get_hint') as mock_hint:
        mock_hint.return_value = "Here's a hint: try using ls"
        
        response = client.get("/api/mentor/hint/0")
        
        assert response.status_code == 200
        assert "hint" in response.json()

def test_explain_command():
    with patch('src.routes.mentor.AIMentor.explain_command') as mock_explain:
        mock_explain.return_value = "The 'ls' command lists directory contents"
        
        response = client.post(
            "/api/mentor/explain",
            json={"command": "ls"}
        )
        
        assert response.status_code == 200
        assert "explanation" in response.json()

# WebSocket Tests
def test_websocket_ssh():
    from src.main import socketio
    
    # Create a test client
    client = socketio.test_client(app, namespace='/')
    
    # Test SSH command execution
    with patch('src.routes.ssh.SSHManager.execute_command') as mock_execute:
        mock_execute.return_value = "test\r\n"
        
        # Emit the command
        client.emit('ssh_command', 
                   {'command': 'echo test', 'session_id': 'test-session-123'})
        
        # Mock the SSH output callback
        with patch.object(client, 'emit') as mock_emit:
            # Simulate SSH output
            mock_emit.side_effect = lambda event, data, **kwargs: client.get_received()
            
            # Get received messages
            received = client.get_received()
            
            # In a real test, we'd need to mock the SSH connection properly
            # For now, we'll just verify the command was sent
            mock_execute.assert_called_once_with('test-session-123', 'echo test')
    
    client.disconnect()

def test_websocket_chat():
    from src.main import socketio
    
    # Create a test client
    client = socketio.test_client(app, namespace='/')
    
    # Test chat message
    with patch('src.routes.mentor.ai_mentor') as mock_mentor:
        mock_mentor.get_response.return_value = "Test response from AI"
        
        # Emit the chat message with required fields
        client.emit('mentor_message', {
            'message': 'Hello, AI',
            'session_id': 'test-session-123',
            'current_level': 0,
            'recent_commands': [],
            'terminal_output': '',
            'timestamp': '2023-01-01T00:00:00Z'
        })
        
        # Get received messages
        received = client.get_received()
        
        # Assert the chat was processed correctly
        assert len(received) > 0
        assert received[0]['name'] == 'mentor_response'
        assert 'Test response from AI' in received[0]['args'][0]['message']
        assert received[0]['args'][0]['session_id'] == 'test-session-123'
    
    client.disconnect()

# Error Handling Tests
def test_invalid_endpoint():
    response = client.get("/api/invalid/endpoint")
    assert response.status_code == 404

def test_unauthorized_access():
    response = client.post("/api/ssh/command", json={"command": "ls"})
    assert response.status_code in [400, 401]  # Bad Request or Unauthorized for missing session_id

# Data Validation Tests
def test_invalid_level_id():
    """Test that non-integer level IDs return 404 Not Found"""
    # Test with non-numeric level ID
    response = client.get("/api/levels/invalid")
    assert response.status_code == 404
    
    # Test with float number as level ID
    response = client.get("/api/levels/1.5")
    assert response.status_code == 404
    
    # Test with negative number (if not supported)
    response = client.get("/api/levels/-1")
    assert response.status_code == 404

def test_malformed_request():
    response = client.post("/api/ssh/connect", json={"invalid": "data"})
    assert response.status_code == 400  # Bad Request for missing required fields

if __name__ == "__main__":
    pytest.main(["-v", "test_api.py"])
