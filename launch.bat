@echo off
setlocal enabledelayedexpansion

echo Starting Bandit Wargame GUI...

REM Function to check if a command exists
where python >nul 2>&1
if errorlevel 1 (
    echo Python is not installed or not in PATH. Please install Python 3.8 or higher.
    pause
    exit /b 1
)

where node >nul 2>&1
if errorlevel 1 (
    echo Node.js is not installed or not in PATH. Please install Node.js 14 or higher.
    pause
    exit /b 1
)

where pnpm >nul 2>&1
if errorlevel 1 (
    echo pnpm is not installed. Installing pnpm globally...
    npm install -g pnpm
    if errorlevel 1 (
        echo Failed to install pnpm. Please install it manually with 'npm install -g pnpm'
        pause
        exit /b 1
    )
)

set BACKEND_PATH=bandit-gui-app\backend\bandit-backend
set VENV_PATH=%BACKEND_PATH%\venv

REM Remove existing venv if it exists to ensure clean state
if exist "%VENV_PATH%" (
    echo Removing existing virtual environment...
    rmdir /s /q "%VENV_PATH%"
)

echo Creating Python virtual environment...
python -m venv "%VENV_PATH%"
if errorlevel 1 (
    echo Failed to create virtual environment.
    pause
    exit /b 1
)

echo Activating virtual environment and installing requirements...
call "%VENV_PATH%\Scripts\activate.bat"
if errorlevel 1 (
    echo Failed to activate virtual environment.
    pause
    exit /b 1
)

pip install -r "%BACKEND_PATH%\requirements.txt"
if errorlevel 1 (
    echo Failed to install Python requirements.
    pause
    exit /b 1
)

REM Create backend startup script
set BACKEND_SCRIPT=%TEMP%\start_backend.bat
echo @echo off > "%BACKEND_SCRIPT%"
echo cd /d "%CD%\%BACKEND_PATH%\src" >> "%BACKEND_SCRIPT%"
echo call "%CD%\%VENV_PATH%\Scripts\activate.bat" >> "%BACKEND_SCRIPT%"
echo python main.py >> "%BACKEND_SCRIPT%"
echo pause >> "%BACKEND_SCRIPT%"

echo Starting backend server...
start "Bandit Backend" cmd /k "%BACKEND_SCRIPT%"

REM Wait for backend to start
timeout /t 5 /nobreak >nul

set FRONTEND_PATH=bandit-gui-app\frontend\bandit-frontend

REM Install frontend dependencies if node_modules doesn't exist
if not exist "%FRONTEND_PATH%\node_modules" (
    echo Installing frontend dependencies...
    cd "%FRONTEND_PATH%"
    pnpm install
    if errorlevel 1 (
        echo Failed to install frontend dependencies.
        pause
        exit /b 1
    )
    cd "%~dp0"
)

REM Create frontend startup script
set FRONTEND_SCRIPT=%TEMP%\start_frontend.bat
echo @echo off > "%FRONTEND_SCRIPT%"
echo cd /d "%CD%\%FRONTEND_PATH%" >> "%FRONTEND_SCRIPT%"
echo pnpm dev >> "%FRONTEND_SCRIPT%"

echo Starting frontend development server...
start "Bandit Frontend" cmd /k "%FRONTEND_SCRIPT%"

REM Wait for frontend to start up properly
echo Waiting for frontend development server to start...
timeout /t 8 /nobreak >nul

REM Open browser to frontend URL
echo Opening browser to frontend application...
start "" "http://localhost:5173"
if errorlevel 1 (
    echo Warning: Failed to open browser automatically. Please manually navigate to http://localhost:5173
) else (
    echo Browser opened successfully.
)

echo.
echo Application is starting up...
echo - Backend: http://localhost:5001
echo - Frontend: http://localhost:5173
echo.
echo Note: The frontend might take a moment to start up.
echo.
pause
