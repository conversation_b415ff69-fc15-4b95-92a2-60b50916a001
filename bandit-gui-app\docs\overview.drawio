<?xml version="1.0" encoding="UTF-8"?>
      <mxfile host="codeviz.app" modified="2025-08-25T13:52:19.956Z" agent="CodeViz Exporter" version="14.6.5" type="device">
        <diagram id="codeviz-diagram" name="System Diagram">
          <mxGraphModel dx="1000" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
              <mxCell id="0"/>
              <mxCell id="1" parent="0"/>
              <mxCell id="2" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1">
                <mxGeometry x="275" y="690" width="390" height="220" as="geometry"/>
              </mxCell>
              <mxCell id="2_label" value="Hooks &amp;amp; Utilities&lt;br&gt;JavaScript" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1">
                <mxGeometry x="283" y="698" width="314" height="24" as="geometry"/>
              </mxCell>
<mxCell id="3" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1">
                <mxGeometry x="50" y="410" width="840" height="220" as="geometry"/>
              </mxCell>
              <mxCell id="3_label" value="UI Components&lt;br&gt;React" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1">
                <mxGeometry x="58" y="418" width="764" height="24" as="geometry"/>
              </mxCell>
<mxCell id="5" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="4">
                <mxGeometry x="350" y="790" width="390" height="380" as="geometry"/>
              </mxCell>
              <mxCell id="5_label" value="Data Management&lt;br&gt;SQLite / JSON" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="4">
                <mxGeometry x="358" y="798" width="314" height="24" as="geometry"/>
              </mxCell>
<mxCell id="6" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="4">
                <mxGeometry x="50" y="500" width="540" height="220" as="geometry"/>
              </mxCell>
              <mxCell id="6_label" value="Core Logic Modules&lt;br&gt;Python" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="4">
                <mxGeometry x="58" y="508" width="464" height="24" as="geometry"/>
              </mxCell>
<mxCell id="7" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="4">
                <mxGeometry x="146.66666666666669" y="250" width="690" height="220" as="geometry"/>
              </mxCell>
              <mxCell id="7_label" value="API Endpoints&lt;br&gt;Flask Routes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="4">
                <mxGeometry x="154.66666666666669" y="258" width="614" height="24" as="geometry"/>
              </mxCell>
<mxCell id="1" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1">
                <mxGeometry x="32" y="282" width="900" height="930" as="geometry"/>
              </mxCell>
              <mxCell id="1_label" value="Frontend System&lt;br&gt;React" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1">
                <mxGeometry x="40" y="290" width="824" height="24" as="geometry"/>
              </mxCell>
<mxCell id="4" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1">
                <mxGeometry x="48.66666666666663" y="1382" width="856.6666666666666" height="1190" as="geometry"/>
              </mxCell>
              <mxCell id="4_label" value="Backend System&lt;br&gt;Python / Flask" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1">
                <mxGeometry x="56.66666666666663" y="1390" width="780.6666666666666" height="24" as="geometry"/>
              </mxCell>
              <mxCell id="8" value="User&lt;br&gt;External Actor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="392" y="12" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="20" value="Bandit Wargame&lt;br&gt;External System" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="4">
                    <mxGeometry x="190" y="815" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="21" value="External AI Service&lt;br&gt;External API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="4">
                    <mxGeometry x="40" y="815" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="22" value="Main Application Entry&lt;br&gt;React" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="380" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="23" value="Core Application Component&lt;br&gt;React" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="380" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="9" value="Main Application&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="4">
                    <mxGeometry x="401.6666666666667" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="10" value="User Routes&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="7">
                    <mxGeometry x="190" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="11" value="SSH Routes&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="7">
                    <mxGeometry x="340" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="12" value="Levels Routes&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="7">
                    <mxGeometry x="40" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="13" value="Mentor Routes&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="7">
                    <mxGeometry x="490" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="14" value="SSH Manager&lt;br&gt;Paramiko" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="6">
                    <mxGeometry x="190" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="15" value="AI Mentor&lt;br&gt;OpenAI API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="6">
                    <mxGeometry x="40" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="16" value="Level Scraper&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="6">
                    <mxGeometry x="340" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="17" value="Application Database&lt;br&gt;SQLite" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="5">
                    <mxGeometry x="190" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="18" value="Bandit Levels Data&lt;br&gt;JSON" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="5">
                    <mxGeometry x="40" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="19" value="User Model&lt;br&gt;SQLAlchemy" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="5">
                    <mxGeometry x="190" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="24" value="AI Mentor Component&lt;br&gt;React" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3">
                    <mxGeometry x="190" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="25" value="Level Info Component&lt;br&gt;React" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3">
                    <mxGeometry x="340" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="26" value="SSH Login Component&lt;br&gt;React" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3">
                    <mxGeometry x="40" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="27" value="Terminal Component&lt;br&gt;React" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3">
                    <mxGeometry x="490" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="28" value="Shadcn UI Components&lt;br&gt;React" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3">
                    <mxGeometry x="640" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="29" value="Mobile Hook&lt;br&gt;React Hook" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="2">
                    <mxGeometry x="40" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="30" value="Utility Functions&lt;br&gt;JavaScript" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="2">
                    <mxGeometry x="190" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
              <mxCell id="edge-12" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="8" target="1">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-12_label" value="interacts with" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-12">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-11" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3" target="2">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-11_label" value="uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-11">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-10" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="23" target="3">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-10_label" value="uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-10">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-13" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="1" target="4">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-13_label" value="makes API calls to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-13">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-4" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="6" target="5">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-4_label" value="persists data in" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-4">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-3" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="7" target="5">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3_label" value="accesses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-2" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="7" target="6">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-2_label" value="orchestrates" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-2">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-1" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="9" target="7">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-1_label" value="dispatches to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-1">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-8" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="19" target="17">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-8_label" value="persists data in" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-8">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-7" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="16" target="18">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-7_label" value="populates" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-7">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-5" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="14" target="20">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-5_label" value="connects to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-5">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-6" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="15" target="21">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-6_label" value="integrates with" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-6">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-9" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="22" target="23">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-9_label" value="renders" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-9">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
            </root>
          </mxGraphModel>
        </diagram>
      </mxfile>