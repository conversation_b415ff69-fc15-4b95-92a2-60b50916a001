import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink, RefreshCw, Target, Terminal, BookOpen } from 'lucide-react';

const LevelInfo = ({ currentLevel = 0 }) => {
  const [levelData, setLevelData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchLevelData = async (level) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/levels/${level}`);
      const data = await response.json();
      
      if (data.success) {
        setLevelData(data.level);
      } else {
        setError(data.error || 'Failed to fetch level data');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLevelData(currentLevel);
  }, [currentLevel]);

  const handleRefresh = () => {
    fetchLevelData(currentLevel);
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="w-5 h-5" />
            <span>Loading Level {currentLevel}...</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-4">
            <RefreshCw className="w-6 h-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full border-red-500">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="text-red-400">Error Loading Level {currentLevel}</span>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="w-4 h-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-400 text-sm">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (!levelData) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>No Level Data</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-400 text-sm">
            No information available for Level {currentLevel}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Target className="w-5 h-5 text-green-400" />
            <span>Level {currentLevel}</span>
          </div>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="w-4 h-4" />
          </Button>
        </CardTitle>
        {levelData.title && (
          <CardDescription>{levelData.title}</CardDescription>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Level Goal */}
        {levelData.goal && (
          <div>
            <h4 className="font-semibold text-green-400 mb-2 flex items-center space-x-2">
              <Target className="w-4 h-4" />
              <span>Objective</span>
            </h4>
            <p className="text-sm text-gray-300 leading-relaxed">
              {levelData.goal}
            </p>
          </div>
        )}

        {/* Recommended Commands */}
        {levelData.commands && levelData.commands.length > 0 && (
          <div>
            <h4 className="font-semibold text-blue-400 mb-2 flex items-center space-x-2">
              <Terminal className="w-4 h-4" />
              <span>Recommended Commands</span>
            </h4>
            <div className="flex flex-wrap gap-2">
              {levelData.commands.map((command, index) => (
                <Badge key={index} variant="secondary" className="font-mono">
                  {command}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Reading Material */}
        {levelData.reading_material && levelData.reading_material.length > 0 && (
          <div>
            <h4 className="font-semibold text-purple-400 mb-2 flex items-center space-x-2">
              <BookOpen className="w-4 h-4" />
              <span>Additional Learning</span>
            </h4>
            <div className="space-y-2">
              {levelData.reading_material.map((material, index) => (
                <a
                  key={index}
                  href={material.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-2 text-sm text-blue-400 hover:text-blue-300 transition-colors"
                >
                  <ExternalLink className="w-3 h-3" />
                  <span>{material.title}</span>
                </a>
              ))}
            </div>
          </div>
        )}

        {/* Level URL */}
        {levelData.url && (
          <div className="pt-2 border-t border-gray-600">
            <a
              href={levelData.url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-2 text-xs text-gray-400 hover:text-gray-300 transition-colors"
            >
              <ExternalLink className="w-3 h-3" />
              <span>View on OverTheWire</span>
            </a>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default LevelInfo;

