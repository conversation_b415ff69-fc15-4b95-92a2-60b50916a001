# Codebase Research Report

## Query
Investigate the "AI Mentor Capabilities" component in detail. Provide a comprehensive analysis of its architecture, responsibilities, and implementation details.

## Answer
# AI Mentor Capabilities Analysis

## High-Level Architecture

```mermaid
graph TB
  frontend["Frontend<br>Application<br>bandit-gui-app/frontend/"]
  backend["Backend<br>Python Application<br>bandit-gui-app/backend/"]
  aiMentorModule["AI Mentor Module<br>Python<br>bandit-gui-app/backend/bandit-backend/src/ai_mentor.py"]
  mentorRoutesModule["Mentor Routes Module<br>Python<br>bandit-gui-app/backend/bandit-backend/src/routes/mentor.py"]

  frontend --> |"Requests"| backend
  backend --> |"Encapsulates"| aiMentorModule
  backend --> |"Exposes via API"| mentorRoutesModule
  mentorRoutesModule --> |"Utilizes"| aiMentorModule
```


The **AI Mentor Capabilities** are primarily implemented within the **backend** of the Bandit GUI application. This functionality is encapsulated in two main Python modules:
*   The **`ai_mentor`** module, located at [bandit-backend/src/ai_mentor.py](bandit-gui-app/backend/bandit-backend/src/ai_mentor.py), which contains the core logic for interacting with an AI model to generate hints or explanations.
*   The **`mentor` routes** module, located at [bandit-backend/src/routes/mentor.py](bandit-gui-app/backend/bandit-backend/src/routes/mentor.py), which exposes the AI mentor functionality via a RESTful API endpoint.

These modules interact with the overall backend application, likely receiving requests from the **frontend** and providing responses.

## Mid-Level Component Interaction

```mermaid
graph TB
  aiMentorModule["ai_mentor Module<br>Python<br>bandit-gui-app/backend/bandit-backend/src/ai_mentor.py"]
  mentorRoutesModule["mentor Routes Module<br>Python<br>bandit-gui-app/backend/bandit-backend/src/routes/mentor.py"]
  externalAILib["External AI Library<br>OpenAI Client<br>N/A"]
  httpLibs["HTTP Libraries<br>httpx/requests<br>N/A"]
  webFramework["Web Framework<br>Flask<br>N/A"]
  frontendApp["Frontend Application<br>React<br>bandit-gui-app/frontend/bandit-frontend/src/components/AIMentor.jsx"]

  aiMentorModule --> |"Uses"| externalAILib
  aiMentorModule --> |"Uses"| httpLibs
  mentorRoutesModule --> |"Utilizes"| aiMentorModule
  mentorRoutesModule --> |"Depends on"| webFramework
  frontendApp --> |"Consumes API"| mentorRoutesModule
```


### **`ai_mentor` Module**

The [ai_mentor.py](bandit-gui-app/backend/bandit-backend/src/ai_mentor.py) module is responsible for the business logic of the AI mentor.

*   **Purpose:** To provide an interface for generating AI-driven hints or explanations for Bandit wargame levels. It abstracts the interaction with an external AI model (e.g., OpenAI's GPT).
*   **Internal Parts:**
    *   It likely contains functions or classes that handle:
        *   Constructing prompts for the AI model based on the current Bandit level context.
        *   Making API calls to the AI model.
        *   Processing and formatting the AI model's responses.
        *   Error handling for AI model interactions.
*   **External Relationships:**
    *   It depends on external libraries for making HTTP requests (e.g., `httpx` or `requests`) and potentially an OpenAI client library.
    *   It is utilized by the [mentor routes](bandit-gui-app/backend/bandit-backend/src/routes/mentor.py) to serve AI mentor requests.

### **`mentor` Routes Module**

The [mentor.py](bandit-gui-app/backend/bandit-backend/src/routes/mentor.py) module defines the API endpoints for the AI mentor.

*   **Purpose:** To expose the AI mentor functionality to the frontend application via HTTP endpoints. It acts as a bridge between the frontend requests and the core AI mentor logic.
*   **Internal Parts:**
    *   It defines one or more API routes (e.g., `/mentor/hint`, `/mentor/explanation`).
    *   Each route handler will:
        *   Receive incoming HTTP requests (e.g., POST requests with level information).
        *   Call the appropriate functions within the [ai_mentor.py](bandit-gui-app/backend/bandit-backend/src/ai_mentor.py) module.
        *   Return the AI mentor's response as an HTTP response (e.g., JSON).
*   **External Relationships:**
    *   It depends on a web framework (e.g., Flask, as indicated by `flask_cors` in `requirements.txt`) to define routes and handle requests/responses.
    *   It interacts directly with the [ai_mentor.py](bandit-gui-app/backend/bandit-backend/src/ai_mentor.py) module to get AI-generated content.
    *   It is consumed by the **frontend** application (e.g., [AIMentor.jsx](bandit-gui-app/frontend/bandit-frontend/src/components/AIMentor.jsx)) to display AI mentor content to the user.

## Low-Level Implementation Details

```mermaid
graph TB
  subgraph aiMentorPy["ai_mentor.py Implementation"]
    banditAIMentorClass["BanditAIMentor<br>Class<br>bandit-gui-app/backend/bandit-backend/src/ai_mentor.py"]
    initMethod["__init__()<br>Method<br>bandit-gui-app/backend/bandit-backend/src/ai_mentor.py"]
    getResponseMethod["get_response()<br>Method<br>bandit-gui-app/backend/bandit-backend/src/ai_mentor.py"]
    clearConversationMethod["clear_conversation()<br>Method<br>bandit-gui-app/backend/bandit-backend/src/ai_mentor.py"]
    getLevelHintMethod["get_level_hint()<br>Method<br>bandit-gui-app/backend/bandit-backend/src/ai_mentor.py"]
    explainCommandMethod["explain_command()<br>Method<br>bandit-gui-app/backend/bandit-backend/src/ai_mentor.py"]
    globalInstance["ai_mentor<br>Global Instance<br>bandit-gui-app/backend/bandit-backend/src/ai_mentor.py"]
  end

  subgraph mentorPy["mentor.py Implementation"]
    mentorBlueprint["mentor_bp<br>Flask Blueprint<br>bandit-gui-app/backend/bandit-backend/src/routes/mentor.py"]
    initSocketIOFunc["init_mentor_socketio()<br>Function<br>bandit-gui-app/backend/bandit-backend/src/routes/mentor.py"]
    socketIOMessage["@socketio.on('mentor_message')<br>Event Handler<br>bandit-gui-app/backend/bandit-backend/src/routes/mentor.py"]
    socketIOClear["@socketio.on('mentor_clear')<br>Event Handler<br>bandit-gui-app/backend/bandit-backend/src/routes/mentor.py"]
    hintRoute["/hint/<level_num><br>GET Route<br>bandit-gui-app/backend/bandit-backend/src/routes/mentor.py"]
    explainRoute["/explain/<command><br>GET Route<br>bandit-gui-app/backend/bandit-backend/src/routes/mentor.py"]
    chatRoute["/chat<br>POST Route<br>bandit-gui-app/backend/bandit-backend/src/routes/mentor.py"]
    clearRoute["/clear/<session_id><br>POST Route<br>bandit-gui-app/backend/bandit-backend/src/routes/mentor.py"]
  end

  banditAIMentorClass --> |"Initializes"| initMethod
  banditAIMentorClass --> |"Uses OpenAI API"| getResponseMethod
  banditAIMentorClass --> |"Manages history"| clearConversationMethod
  banditAIMentorClass --> |"Provides static hint"| getLevelHintMethod
  banditAIMentorClass --> |"Provides static explanation"| explainCommandMethod
  globalInstance --> |"Is instance of"| banditAIMentorClass

  initSocketIOFunc --> |"Registers"| socketIOMessage
  initSocketIOFunc --> |"Registers"| socketIOClear
  socketIOMessage --> |"Calls"| getResponseMethod
  socketIOClear --> |"Calls"| clearConversationMethod
  hintRoute --> |"Calls"| getLevelHintMethod
  explainRoute --> |"Calls"| explainCommandMethod
  chatRoute --> |"Calls"| getResponseMethod
  clearRoute --> |"Calls"| clearConversationMethod

  mentorBlueprint --> |"Defines"| hintRoute
  mentorBlueprint --> |"Defines"| explainRoute
  mentorBlueprint --> |"Defines"| chatRoute
  mentorBlueprint --> |"Defines"| clearRoute
```


### **`ai_mentor.py` Implementation**

To understand the exact implementation, I will read the content of [ai_mentor.py](bandit-gui-app/backend/bandit-backend/src/ai_mentor.py).
### **`ai_mentor.py` Implementation**

The [bandit-backend/src/ai_mentor.py](bandit-gui-app/backend/bandit-backend/src/ai_mentor.py) file defines the `BanditAIMentor` class, which encapsulates the logic for interacting with the OpenAI API and managing conversation history.

*   **Class:** `BanditAIMentor`
    *   **`__init__(self)`:**
        *   Initializes `self.client` using `openai.OpenAI()`, expecting API keys to be configured via environment variables.
        *   Maintains `self.conversation_history` as a dictionary to store chat history per `session_id`.
        *   Sets a detailed `self.system_prompt` that guides the AI's behavior, emphasizing guidance without direct solutions, encouraging experimentation, and incorporating a "dark humoristic, and sarcastic" tone.
    *   **`get_response(self, user_message: str, session_id: str = "default", current_level: int = 0, recent_commands: List[str] = None, terminal_output: str = "") -> str`:**
        *   This is the primary method for generating AI-driven responses.
        *   It constructs a context message using `current_level`, `recent_commands` (last 5), and `terminal_output` (last 500 characters).
        *   It prepares messages for the OpenAI API, including the `system_prompt`, the last 10 exchanges from `conversation_history`, the current context, and the `user_message`.
        *   It calls `self.client.chat.completions.create` using the `gpt-3.5-turbo` model.
        *   The AI's response is then appended to the `conversation_history` for the given `session_id`.
        *   Includes a `try-except` block for error handling during API calls.
    *   **`clear_conversation(self, session_id: str = "default")`:**
        *   Removes the conversation history for a specified `session_id`.
    *   **`get_level_hint(self, level_num: int) -> str`:**
        *   Provides pre-defined, static hints for specific Bandit levels (e.g., [bandit-backend/src/ai_mentor.py:69](bandit-gui-app/backend/bandit-backend/src/ai_mentor.py:69)). This is not an AI-generated hint.
    *   **`explain_command(self, command: str) -> str`:**
        *   Offers pre-defined, static explanations for common Linux commands (e.g., [bandit-backend/src/ai_mentor.py:85](bandit-gui-app/backend/bandit-backend/src/ai_mentor.py:85)). This is not an AI-generated explanation.

A global instance `ai_mentor = BanditAIMentor()` is created at the end of the file, making it readily available for import and use by other modules.

### **`mentor.py` Implementation**

Now, I will read the content of [bandit-backend/src/routes/mentor.py](bandit-gui-app/backend/bandit-backend/src/routes/mentor.py) to understand how the `BanditAIMentor` is exposed via API routes.
### **`mentor.py` Implementation**

The [bandit-backend/src/routes/mentor.py](bandit-gui-app/backend/bandit-backend/src/routes/mentor.py) file defines the API endpoints and SocketIO event handlers that expose the AI mentor's capabilities to the frontend.

*   **Imports:** It imports `Blueprint`, `request`, `jsonify` from `flask`, `emit` from `flask_socketio`, and the global `ai_mentor` instance from [src/ai_mentor.py](bandit-gui-app/backend/bandit-backend/src/ai_mentor.py).
*   **`mentor_bp = Blueprint('mentor', __name__)`:** A Flask Blueprint is created to organize mentor-related routes.
*   **`init_mentor_socketio(socketio)` function:** This function is designed to be called to register SocketIO event handlers.
    *   **`@socketio.on('mentor_message')`:**
        *   **Purpose:** Handles real-time chat messages from the frontend.
        *   **Input:** Expects `data` containing `message`, `session_id`, `current_level`, `recent_commands`, and `terminal_output`.
        *   **Process:** Calls `ai_mentor.get_response()` with the provided context.
        *   **Output:** Emits a `mentor_response` SocketIO event back to the client with the AI's reply.
    *   **`@socketio.on('mentor_clear')`:**
        *   **Purpose:** Clears the conversation history for a specific session.
        *   **Input:** Expects `data` containing `session_id`.
        *   **Process:** Calls `ai_mentor.clear_conversation()`.
        *   **Output:** Emits a `mentor_cleared` SocketIO event.
*   **REST API Endpoints (Flask Routes):**
    *   **`@mentor_bp.route('/hint/<int:level_num>', methods=['GET'])`:**
        *   **Purpose:** Provides a general hint for a specific Bandit level.
        *   **Process:** Calls `ai_mentor.get_level_hint(level_num)`.
        *   **Output:** Returns the hint as a JSON response.
    *   **`@mentor_bp.route('/explain/<command>', methods=['GET'])`:**
        *   **Purpose:** Provides an explanation for a given Linux command.
        *   **Process:** Calls `ai_mentor.explain_command(command)`.
        *   **Output:** Returns the explanation as a JSON response.
    *   **`@mentor_bp.route('/chat', methods=['POST'])`:**
        *   **Purpose:** A RESTful endpoint for chatting with the AI mentor, providing an alternative to the SocketIO interface for chat.
        *   **Input:** Expects a JSON payload with `message`, `session_id`, `current_level`, `recent_commands`, and `terminal_output`.
        *   **Process:** Calls `ai_mentor.get_response()` with the provided context.
        *   **Output:** Returns the AI's response as a JSON response.
    *   **`@mentor_bp.route('/clear/<session_id>', methods=['POST'])`:**
        *   **Purpose:** Clears the conversation history for a specific session via a REST call.
        *   **Process:** Calls `ai_mentor.clear_conversation(session_id)`.
        *   **Output:** Returns a success message as a JSON response.

## Conclusion

```mermaid
graph TB
  banditAIMentor["BanditAIMentor<br>Core AI Logic<br>bandit-gui-app/backend/bandit-backend/src/ai_mentor.py"]
  mentorRoutes["Mentor Routes<br>API Endpoints<br>bandit-gui-app/backend/bandit-backend/src/routes/mentor.py"]
  openAIApi["OpenAI API<br>External Service<br>N/A"]
  frontendApp["Frontend Application<br>User Interface<br>bandit-gui-app/frontend/"]
  socketIO["SocketIO<br>Real-time Communication<br>N/A"]
  restApi["REST API<br>HTTP Communication<br>N/A"]

  banditAIMentor --> |"Interacts with"| openAIApi
  mentorRoutes --> |"Exposes via"| socketIO
  mentorRoutes --> |"Exposes via"| restApi
  mentorRoutes --> |"Utilizes"| banditAIMentor
  frontendApp --> |"Communicates via"| socketIO
  frontendApp --> |"Communicates via"| restApi
  socketIO --> |"Provides chat"| banditAIMentor
  restApi --> |"Provides hints/chat"| banditAIMentor
```


The **AI Mentor Capabilities** are a well-integrated feature within the Bandit GUI backend. The core AI logic resides in the [BanditAIMentor](bandit-gui-app/backend/bandit-backend/src/ai_mentor.py) class, which handles interaction with the OpenAI API, conversation history management, and context building. This functionality is exposed to the frontend through both real-time SocketIO events and traditional REST API endpoints defined in [bandit-backend/src/routes/mentor.py](bandit-gui-app/backend/bandit-backend/src/routes/mentor.py). This dual approach provides flexibility for the frontend to interact with the mentor, whether for dynamic chat or static hints/explanations. The system is designed to be a guiding tool, adhering to strict rules about not providing direct solutions, and aims to foster learning through contextualized hints and explanations.

---
*Generated by [CodeViz.ai](https://codeviz.ai) on 8/25/2025, 9:59:46 AM*
