import { useState, useEffect, useRef } from 'react';
import { io } from 'socket.io-client';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import 'xterm/css/xterm.css';
import SSHLogin from './components/SSHLogin';
import LevelInfo from './components/LevelInfo';
import AIMentor from './components/AIMentor';
import './App.css';

function App() {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const [currentLevel, setCurrentLevel] = useState(0);
  const [recentCommands, setRecentCommands] = useState([]);
  const [connectionError, setConnectionError] = useState(null);
  const [terminalInput, setTerminalInput] = useState('');
  const [mentorQuestion, setMentorQuestion] = useState('');
  const termRef = useRef(null);
  const fitAddon = useRef(new FitAddon());
  const terminal = useRef(null);

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io('http://localhost:5001', {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    });
    
    newSocket.on('connect', () => {
      console.log('Connected to server');
      setConnectionError(null);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
      setIsConnected(false);
    });

    newSocket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      setConnectionError('Failed to connect to backend server. Please ensure the backend is running on port 5001.');
    });

    newSocket.on('ssh_connected', (data) => {
      if (data.session_id === sessionId) {
        setIsConnected(true);
        setConnectionError(null);
      }
    });

    newSocket.on('ssh_disconnected', (data) => {
      if (data.session_id === sessionId) {
        setIsConnected(false);
      }
    });

    // Track commands for AI mentor
    newSocket.on('ssh_command', (data) => {
      if (data.session_id === sessionId && data.command) {
        const command = data.command.trim();
        if (command && !command.startsWith('\r') && !command.startsWith('\n')) {
          setRecentCommands(prev => {
            const updated = [command, ...prev.slice(0, 9)]; // Keep last 10 commands
            return updated;
          });
        }
      }
    });

    // Detect level changes from SSH output
    newSocket.on('ssh_output', (data) => {
      if (data.session_id === sessionId && data.data) {
        const output = data.data;
        // Look for bandit level indicators in the prompt
        const levelMatch = output.match(/bandit(\d+)@/);
        if (levelMatch) {
          const detectedLevel = parseInt(levelMatch[1], 10);
          if (detectedLevel !== currentLevel) {
            setCurrentLevel(detectedLevel);
          }
        }
      }
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [sessionId]);

  const handleSSHConnect = (username, password) => {
    return new Promise((resolve, reject) => {
      if (!socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      // Extract level from username (e.g., bandit0 -> 0)
      const levelMatch = username.match(/bandit(\d+)/);
      if (levelMatch) {
        setCurrentLevel(parseInt(levelMatch[1], 10));
      }

      socket.emit('ssh_connect', {
        hostname: 'bandit.labs.overthewire.org',
        port: 2220,
        username,
        password,
        session_id: sessionId
      });
      
      // Listen for connection result
      const handleConnected = (data) => {
        if (data.session_id === sessionId) {
          socket.off('ssh_connected', handleConnected);
          socket.off('ssh_error', handleError);
          setConnectionError(null);
          resolve();
        }
      };
      
      const handleError = (data) => {
        if (data.session_id === sessionId) {
          socket.off('ssh_connected', handleConnected);
          socket.off('ssh_error', handleError);
          setConnectionError(data.message || 'SSH connection failed');
          reject(new Error(data.message || 'SSH connection failed'));
        }
      };
      
      socket.on('ssh_connected', handleConnected);
      socket.on('ssh_error', handleError);

      // Timeout after 30 seconds
      setTimeout(() => {
        socket.off('ssh_connected', handleConnected);
        socket.off('ssh_error', handleError);
        reject(new Error('Connection timeout'));
      }, 30000);
    });
  };

  const handleDisconnect = () => {
    if (socket) {
      socket.emit('ssh_disconnect', { session_id: sessionId });
      setIsConnected(false);
      setRecentCommands([]);
    }
  };

  // Handle terminal input submission
  const handleTerminalSubmit = (e) => {
    e.preventDefault();
    if (terminalInput.trim() && socket && isConnected) {
      socket.emit('ssh_command', {
        command: terminalInput + '\n',
        session_id: sessionId
      });
      setTerminalInput('');
    }
  };

  // Handle AI Mentor question submission
  const handleMentorSubmit = (e) => {
    e.preventDefault();
    if (mentorQuestion.trim() && socket) {
      // Emit the question to the AI mentor
      socket.emit('mentor_question', {
        question: mentorQuestion,
        session_id: sessionId,
        current_level: currentLevel,
        recent_commands: recentCommands
      });
      setMentorQuestion('');
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      <div className="container mx-auto p-4">
        <header className="mb-6">
          <h1 className="text-3xl font-bold text-center text-yellow-400">
            Bandit Wargame GUI
          </h1>
          <p className="text-center text-gray-400 mt-2">
            A simplified interface for playing OverTheWire Bandit challenges
          </p>
          {connectionError && (
            <div className="mt-4 p-3 bg-red-600 text-white rounded-lg text-center text-sm">
              <strong>Connection Error:</strong> {connectionError}
            </div>
          )}
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
          {/* Terminal Panel */}
          <div className="lg:col-span-2 bg-slate-800 rounded-lg border border-green-800 overflow-hidden flex flex-col">
            <div className="flex-1 overflow-hidden">
              <Terminal 
                socket={socket} 
                sessionId={sessionId}
                onDisconnect={handleDisconnect}
                ref={termRef}
              />
            </div>
            <form onSubmit={handleTerminalSubmit} className="p-2 bg-slate-700 border-t border-green-800">
              <div className="flex">
                <span className="inline-flex items-center px-3 text-sm text-gray-400 bg-slate-600 border border-r-0 border-green-700 rounded-l-md">
                  $
                </span>
                <input
                  type="text"
                  value={terminalInput}
                  onChange={(e) => setTerminalInput(e.target.value)}
                  className="flex-1 min-w-0 block w-full px-3 py-2 text-white bg-slate-700 border border-green-700 rounded-r-md focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter command..."
                  disabled={!isConnected}
                />
                <button
                  type="submit"
                  disabled={!isConnected || !terminalInput.trim()}
                  className="ml-2 px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Send
                </button>
              </div>
            </form>
          </div>

          {/* Side Panel */}
          <div className="space-y-6 flex flex-col">
            {/* SSH Login */}
            <SSHLogin 
              onConnect={handleSSHConnect}
              isConnected={isConnected}
              onDisconnect={handleDisconnect}
              connectionError={connectionError}
            />

            {/* Level Information */}
            <LevelInfo currentLevel={currentLevel} />

            {/* AI Mentor */}
            <div className="flex-1 flex flex-col bg-slate-800 rounded-lg border border-green-800 overflow-hidden">
              <div className="flex-1 overflow-auto p-4">
                <AIMentor 
                  socket={socket}
                  sessionId={sessionId}
                  currentLevel={currentLevel}
                  recentCommands={recentCommands}
                />
              </div>
              <form onSubmit={handleMentorSubmit} className="p-2 bg-slate-700 border-t border-green-800">
                <div className="flex">
                  <input
                    type="text"
                    value={mentorQuestion}
                    onChange={(e) => setMentorQuestion(e.target.value)}
                    className="flex-1 min-w-0 block w-full px-3 py-2 text-white bg-slate-700 border border-green-700 rounded-l-md focus:ring-yellow-500 focus:border-yellow-500"
                    placeholder="Ask the AI Mentor a question..."
                  />
                  <button
                    type="submit"
                    disabled={!mentorQuestion.trim()}
                    className="ml-0 px-4 py-2 text-sm font-medium text-white bg-yellow-600 rounded-r-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Ask
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        {/* Footer with instructions */}
        <footer className="mt-6 text-center text-gray-500 text-sm">
          <p>
            Access this application at <code className="bg-slate-800 px-2 py-1 rounded">http://localhost:5173</code>
          </p>
          <p className="mt-1">
            Ensure backend server is running on port 5001 for full functionality
          </p>
        </footer>
      </div>
    </div>
  );
}

export default App;
