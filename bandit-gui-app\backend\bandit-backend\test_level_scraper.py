"""
Tests for the Level Scraper module
"""
import pytest
import json
import os
from unittest.mock import patch, MagicMock, mock_open
from bs4 import BeautifulSoup
from src.level_scraper import LevelScraper, LevelManager

# Sample HTML content for testing
SAMPLE_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>Bandit Level 0</title>
</head>
<body>
    <h1>Level 0</h1>
    <div id="content">
        <h2>Level Goal</h2>
        <p>The goal of this level is to find the password for Level 1.</p>
        
        <h2>Commands you may need to solve this level</h2>
        <ul>
            <li><code>ls</code> - List directory contents</li>
            <li><code>cd</code> - Change directory</li>
            <li><code>cat</code> - Print file contents</li>
        </ul>
        
        <h2>Helpful Reading Material</h2>
        <ul>
            <li><a href="https://linux.die.net/man/1/ls">man ls</a></li>
            <li><a href="https://linux.die.net/man/1/cat">man cat</a></li>
        </ul>
    </div>
</body>
</html>
"""

@pytest.fixture
def mock_requests_get():
    with patch('requests.get') as mock_get:
        yield mock_get

@pytest.fixture
def mock_file_operations():
    with patch('builtins.open', mock_open()) as mock_file, \
         patch('json.load', return_value={}), \
         patch('json.dump'):
        yield mock_file

@pytest.fixture
def level_scraper():
    return LevelScraper()

@pytest.fixture
def level_manager():
    return LevelManager()

def test_parse_level_content(level_scraper):
    """Test parsing level content from HTML"""
    soup = BeautifulSoup(SAMPLE_HTML, 'html.parser')
    level_data = level_scraper._parse_level_content(soup, 0)
    
    assert level_data["level"] == 0
    assert "The goal of this level is to find the password for Level 1." in level_data["goal"]
    assert "ls" in level_data["commands"]
    assert "cd" in level_data["commands"]
    assert "cat" in level_data["commands"]
    assert "https://linux.die.net/man/1/ls" in level_data["reading_materials"]
    assert "https://linux.die.net/man/1/cat" in level_data["reading_materials"]

def test_fetch_level_success(level_scraper, mock_requests_get):
    """Test successful level data fetching"""
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.text = SAMPLE_HTML
    mock_requests_get.return_value = mock_response
    
    level_data = level_scraper.fetch_level(0)
    
    assert level_data is not None
    assert level_data["level"] == 0
    mock_requests_get.assert_called_once()

def test_fetch_level_not_found(level_scraper, mock_requests_get):
    """Test handling of non-existent level"""
    mock_response = MagicMock()
    mock_response.status_code = 404
    mock_requests_get.return_value = mock_response
    
    with pytest.raises(ValueError):
        level_scraper.fetch_level(999)

def test_save_and_load_levels(level_scraper, tmp_path):
    """Test saving and loading levels to/from file"""
    test_file = tmp_path / "test_levels.json"
    test_data = {
        "0": {
            "level": 0,
            "goal": "Test goal",
            "commands": ["ls", "cat"],
            "reading_materials": ["man ls"]
        }
    }
    
    # Test saving
    level_scraper._save_levels(test_data, str(test_file))
    
    # Test loading
    with patch('builtins.open', mock_open(read_data=json.dumps(test_data))):
        loaded_data = level_scraper._load_levels(str(test_file))
        assert loaded_data == test_data

def test_refresh_levels(level_scraper, mock_requests_get, mock_file_operations):
    """Test refreshing all levels"""
    # Mock successful response for level 0
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.text = SAMPLE_HTML
    mock_requests_get.return_value = mock_response
    
    # Mock file operations
    with patch('os.path.exists', return_value=False), \
         patch('json.dump') as mock_dump:
        
        level_scraper.refresh_levels()
        
        # Verify levels were fetched and saved
        assert mock_requests_get.call_count > 0
        mock_dump.assert_called_once()

def test_level_manager_get_level(level_manager):
    """Test getting a specific level"""
    test_data = {
        "0": {"level": 0, "goal": "Test goal", "commands": [], "reading_materials": []}
    }
    
    with patch('src.level_scraper.LevelScraper._load_levels', return_value=test_data):
        level = level_manager.get_level(0)
        assert level["level"] == 0
        assert level["goal"] == "Test goal"

def test_level_manager_search_levels(level_manager):
    """Test searching levels"""
    test_data = {
        "0": {"level": 0, "goal": "Find password", "commands": [], "reading_materials": []},
        "1": {"level": 1, "goal": "Find file", "commands": [], "reading_materials": []}
    }
    
    with patch('src.level_scraper.LevelScraper._load_levels', return_value=test_data):
        # Search for levels containing 'password'
        results = level_manager.search_levels("password")
        assert len(results) == 1
        assert results[0]["level"] == 0
        
        # Search for non-existent term
        results = level_manager.search_levels("nonexistent")
        assert len(results) == 0

def test_level_manager_refresh_levels(level_manager):
    """Test refreshing levels"""
    with patch('src.level_scraper.LevelScraper.refresh_levels') as mock_refresh, \
         patch('src.level_scraper.LevelScraper._load_levels', return_value={}):
        
        level_manager.refresh_levels()
        mock_refresh.assert_called_once()

def test_level_manager_get_all_levels(level_manager):
    """Test getting all levels"""
    test_data = {
        "0": {"level": 0, "goal": "Test 1"},
        "1": {"level": 1, "goal": "Test 2"}
    }
    
    with patch('src.level_scraper.LevelScraper._load_levels', return_value=test_data):
        levels = level_manager.get_all_levels()
        assert len(levels) == 2
        assert levels[0]["level"] == 0
        assert levels[1]["level"] == 1

def test_level_manager_get_levels_count(level_manager):
    """Test getting the count of levels"""
    test_data = {"0": {}, "1": {}, "2": {}}
    
    with patch('src.level_scraper.LevelScraper._load_levels', return_value=test_data):
        count = level_manager.get_levels_count()
        assert count == 3

def test_level_manager_get_levels_in_range(level_manager):
    """Test getting levels within a specific range"""
    test_data = {
        "0": {"level": 0, "goal": "Level 0"},
        "1": {"level": 1, "goal": "Level 1"},
        "2": {"level": 2, "goal": "Level 2"}
    }
    
    with patch('src.level_scraper.LevelScraper._load_levels', return_value=test_data):
        # Get levels 1-2
        levels = level_manager.get_levels_in_range(1, 2)
        assert len(levels) == 2
        assert levels[0]["level"] == 1
        assert levels[1]["level"] == 2
        
        # Test with invalid range
        with pytest.raises(ValueError):
            level_manager.get_levels_in_range(2, 1)
