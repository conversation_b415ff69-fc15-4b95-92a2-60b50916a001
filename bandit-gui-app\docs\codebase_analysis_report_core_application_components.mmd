graph TD

    terminal-69983580b1be33b446c4ee837bdf434c["Terminal"]
    subgraph search-results-group-69983580b1be33b446c4ee837bdf434c["Search Results"]
        search-result-69983580b1be33b446c4ee837bdf434c["search-result-69983580b1be33b446c4ee837bdf434c"]
        report-section-69983580b1be33b446c4ee837bdf434c-0["report-section-69983580b1be33b446c4ee837bdf434c-0"]
        report-section-69983580b1be33b446c4ee837bdf434c-1["report-section-69983580b1be33b446c4ee837bdf434c-1"]
        report-section-69983580b1be33b446c4ee837bdf434c-2["report-section-69983580b1be33b446c4ee837bdf434c-2"]
        subgraph section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper["High-Level Architecture"]
            aiMentor_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["AI Mentor Capabilities<br>Service"]
            backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Backend Application<br>Node.js/Python"]
            banditServers_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Bandit Wargame Servers<br>SSH Target"]
            frontendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Frontend Application<br>React"]
            %% Edges at this level (grouped by source)
            frontendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Frontend Application<br>React"] -->|WebSockets| backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Backend Application<br>Node.js/Python"]
            backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Backend Application<br>Node.js/Python"] -->|SSH Connection| banditServers_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Bandit Wargame Servers<br>SSH Target"]
            backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["Backend Application<br>Node.js/Python"] -->|Provides| aiMentor_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture["AI Mentor Capabilities<br>Service"]
        end
        subgraph section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper["Core Application Component: [App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)"]
            aiMentorComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["AIMentor Component<br>React Component"]
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"]
            backendServer_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["Backend Server<br>Node.js/Socket.IO"]
            levelInfo_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["LevelInfo Component<br>React Component"]
            sshLogin_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["SSHLogin Component<br>React Component"]
            terminalComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["Terminal Component<br>xterm.js Wrapper"]
            webSocketClient_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["WebSocket Client<br>socket.io-client"]
            xtermJs_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["xterm.js Library<br>Terminal Emulator"]
            %% Edges at this level (grouped by source)
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"] -->|Manages State| webSocketClient_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["WebSocket Client<br>socket.io-client"]
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"] -->|Renders| sshLogin_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["SSHLogin Component<br>React Component"]
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"] -->|Renders| levelInfo_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["LevelInfo Component<br>React Component"]
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"] -->|Renders| aiMentorComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["AIMentor Component<br>React Component"]
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"] -->|Renders| terminalComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["Terminal Component<br>xterm.js Wrapper"]
            appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"] -->|Emits/Listens Events| backendServer_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["Backend Server<br>Node.js/Socket.IO"]
            webSocketClient_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["WebSocket Client<br>socket.io-client"] -->|Connects to| backendServer_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["Backend Server<br>Node.js/Socket.IO"]
            sshLogin_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["SSHLogin Component<br>React Component"] -->|Triggers SSH Connect| appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["App Component<br>React Functional Component"]
            terminalComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["Terminal Component<br>xterm.js Wrapper"] -->|Uses| xtermJs_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)["xterm.js Library<br>Terminal Emulator"]
        end
        %% Edges at this level (grouped by source)
        report-section-69983580b1be33b446c4ee837bdf434c-1["report-section-69983580b1be33b446c4ee837bdf434c-1"] -->|Diagram| section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper["High-Level Architecture"]
        report-section-69983580b1be33b446c4ee837bdf434c-2["report-section-69983580b1be33b446c4ee837bdf434c-2"] -->|Diagram| section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper["Core Application Component: [App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)"]
        search-result-69983580b1be33b446c4ee837bdf434c["search-result-69983580b1be33b446c4ee837bdf434c"] --> report-section-69983580b1be33b446c4ee837bdf434c-0["report-section-69983580b1be33b446c4ee837bdf434c-0"]
        report-section-69983580b1be33b446c4ee837bdf434c-0["report-section-69983580b1be33b446c4ee837bdf434c-0"] --> report-section-69983580b1be33b446c4ee837bdf434c-1["report-section-69983580b1be33b446c4ee837bdf434c-1"]
        report-section-69983580b1be33b446c4ee837bdf434c-0["report-section-69983580b1be33b446c4ee837bdf434c-0"] --> report-section-69983580b1be33b446c4ee837bdf434c-2["report-section-69983580b1be33b446c4ee837bdf434c-2"]
    end
    %% Edges at this level (grouped by source)
    search-results-group-69983580b1be33b446c4ee837bdf434c["Search Results"] -->|Provides context to| terminal-69983580b1be33b446c4ee837bdf434c["Terminal"]
