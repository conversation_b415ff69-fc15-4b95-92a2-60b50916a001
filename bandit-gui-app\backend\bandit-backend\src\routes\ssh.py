from flask import Blueprint
from flask_socketio import <PERSON><PERSON><PERSON>, emit, disconnect
from src.ssh_manager import SSHManager
import json

ssh_bp = Blueprint('ssh', __name__)
ssh_manager = SSHManager()

def init_socketio(socketio: SocketIO):
    """Initialize SocketIO events for SSH communication"""
    
    @socketio.on('connect')
    def handle_connect():
        print('Client connected')
        emit('status', {'message': 'Connected to server'})
    
    @socketio.on('disconnect')
    def handle_disconnect():
        print('Client disconnected')
        # Clean up any SSH connections for this client
        # Note: In a production app, you'd want to track sessions per client
    
    @socketio.on('ssh_connect')
    def handle_ssh_connect(data):
        """Handle SSH connection request"""
        try:
            hostname = data.get('hostname', 'bandit.labs.overthewire.org')
            port = data.get('port', 2220)
            username = data.get('username')
            password = data.get('password')
            session_id = data.get('session_id', 'default')
            
            if not username or not password:
                emit('ssh_error', {'message': 'Username and password required'})
                return
            
            # Create SSH connection
            success = ssh_manager.create_connection(session_id, hostname, port, username, password)
            
            if success:
                connection = ssh_manager.get_connection(session_id)
                if connection:
                    # Set up output callback to send data to client
                    def output_callback(data):
                        socketio.emit('ssh_output', {'data': data, 'session_id': session_id})
                    
                    connection.set_output_callback(output_callback)
                    emit('ssh_connected', {'session_id': session_id, 'message': 'SSH connection established'})
            else:
                emit('ssh_error', {'message': 'Failed to connect to SSH server'})
                
        except Exception as e:
            emit('ssh_error', {'message': f'Connection error: {str(e)}'})
    
    @socketio.on('ssh_command')
    def handle_ssh_command(data):
        """Handle SSH command execution"""
        try:
            session_id = data.get('session_id', 'default')
            command = data.get('command', '')
            
            connection = ssh_manager.get_connection(session_id)
            if connection and connection.connected:
                connection.send_command(command)
            else:
                emit('ssh_error', {'message': 'No active SSH connection'})
                
        except Exception as e:
            emit('ssh_error', {'message': f'Command error: {str(e)}'})
    
    @socketio.on('ssh_disconnect')
    def handle_ssh_disconnect(data):
        """Handle SSH disconnection request"""
        try:
            session_id = data.get('session_id', 'default')
            ssh_manager.disconnect_session(session_id)
            emit('ssh_disconnected', {'session_id': session_id, 'message': 'SSH connection closed'})
            
        except Exception as e:
            emit('ssh_error', {'message': f'Disconnect error: {str(e)}'})

# REST API endpoints for SSH management
@ssh_bp.route('/sessions', methods=['GET'])
def get_sessions():
    """Get list of active SSH sessions"""
    sessions = list(ssh_manager.connections.keys())
    return {'sessions': sessions}

@ssh_bp.route('/session/<session_id>/status', methods=['GET'])
def get_session_status(session_id):
    """Get status of specific SSH session"""
    connection = ssh_manager.get_connection(session_id)
    if connection:
        return {
            'session_id': session_id,
            'connected': connection.connected,
            'hostname': connection.hostname,
            'username': connection.username
        }
    return {'error': 'Session not found'}, 404

