<?xml version="1.0" encoding="UTF-8"?>
      <mxfile host="codeviz.app" modified="2025-08-25T13:56:01.611Z" agent="CodeViz Exporter" version="14.6.5" type="device">
        <diagram id="codeviz-diagram" name="System Diagram">
          <mxGraphModel dx="1000" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
              <mxCell id="0"/>
              <mxCell id="1" parent="0"/>
              <mxCell id="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                <mxGeometry x="50" y="470" width="390" height="540" as="geometry"/>
              </mxCell>
              <mxCell id="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper_label" value="High-Level Architecture" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                <mxGeometry x="58" y="478" width="314" height="24" as="geometry"/>
              </mxCell>
<mxCell id="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                <mxGeometry x="430" y="440" width="724" height="700" as="geometry"/>
              </mxCell>
              <mxCell id="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper_label" value="Core Application Component: [App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                <mxGeometry x="438" y="448" width="648" height="24" as="geometry"/>
              </mxCell>
              <mxCell id="search-results-group-69983580b1be33b446c4ee837bdf434c" value="Search Results" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="12" y="12" width="1144" height="1130" as="geometry"/>
                  </mxCell>
<mxCell id="terminal-69983580b1be33b446c4ee837bdf434c" value="Terminal" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="524" y="1342" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="search-result-69983580b1be33b446c4ee837bdf434c" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                    <mxGeometry x="175" y="80" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="aiMentor_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" value="AI Mentor Capabilities&lt;br&gt;Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper">
                    <mxGeometry x="40" y="440" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" value="Backend Application&lt;br&gt;Node.js/Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper">
                    <mxGeometry x="60" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="banditServers_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" value="Bandit Wargame Servers&lt;br&gt;SSH Target" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper">
                    <mxGeometry x="190" y="410" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="frontendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" value="Frontend Application&lt;br&gt;React" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper">
                    <mxGeometry x="60" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="aiMentorComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="AIMentor Component&lt;br&gt;React Component" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="524" y="440" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="App Component&lt;br&gt;React Functional Component" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="484" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="backendServer_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="Backend Server&lt;br&gt;Node.js/Socket.IO" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="354" y="600" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="levelInfo_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="LevelInfo Component&lt;br&gt;React Component" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="40" y="410" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="sshLogin_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="SSHLogin Component&lt;br&gt;React Component" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="484" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="terminalComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="Terminal Component&lt;br&gt;xterm.js Wrapper" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="190" y="420" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="webSocketClient_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="WebSocket Client&lt;br&gt;socket.io-client" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="374" y="440" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="xtermJs_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" value="xterm.js Library&lt;br&gt;Terminal Emulator" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                    <mxGeometry x="190" y="580" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-69983580b1be33b446c4ee837bdf434c-0" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                    <mxGeometry x="175" y="190" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-69983580b1be33b446c4ee837bdf434c-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                    <mxGeometry x="155" y="300" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="report-section-69983580b1be33b446c4ee837bdf434c-2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="search-results-group-69983580b1be33b446c4ee837bdf434c">
                    <mxGeometry x="702" y="270" width="120" height="60" as="geometry"/>
                  </mxCell>
              <mxCell id="edge-edge-search-to-terminal-69983580b1be33b446c4ee837bdf434c" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="search-results-group-69983580b1be33b446c4ee837bdf434c" target="terminal-69983580b1be33b446c4ee837bdf434c">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-edge-search-to-terminal-69983580b1be33b446c4ee837bdf434c_label" value="Provides context to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-edge-search-to-terminal-69983580b1be33b446c4ee837bdf434c">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_frontendApp_backendApp_0_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="frontendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" target="backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_frontendApp_backendApp_0_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture_label" value="WebSockets" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_frontendApp_backendApp_0_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_backendApp_banditServers_1_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" target="banditServers_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_backendApp_banditServers_1_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture_label" value="SSH Connection" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_backendApp_banditServers_1_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_backendApp_aiMentor_2_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="backendApp_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture" target="aiMentor_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_backendApp_aiMentor_2_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture_label" value="Provides" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_backendApp_aiMentor_2_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-report-section-69983580b1be33b446c4ee837bdf434c-1-to-section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-69983580b1be33b446c4ee837bdf434c-1" target="section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-69983580b1be33b446c4ee837bdf434c-1-to-section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-report-section-69983580b1be33b446c4ee837bdf434c-1-to-section-diagram-69983580b1be33b446c4ee837bdf434c-High-Level-Architecture-wrapper">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_webSocketClient_0_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="webSocketClient_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_webSocketClient_0_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Manages State" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_webSocketClient_0_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_sshLogin_1_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="sshLogin_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_sshLogin_1_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Renders" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_sshLogin_1_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_levelInfo_2_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="levelInfo_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_levelInfo_2_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Renders" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_levelInfo_2_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_aiMentorComponent_3_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="aiMentorComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_aiMentorComponent_3_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Renders" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_aiMentorComponent_3_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_terminalComponent_4_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="terminalComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_terminalComponent_4_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Renders" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_terminalComponent_4_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_webSocketClient_backendServer_5_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="webSocketClient_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="backendServer_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_webSocketClient_backendServer_5_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Connects to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_webSocketClient_backendServer_5_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_sshLogin_appComponent_6_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="sshLogin_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_sshLogin_appComponent_6_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Triggers SSH Connect" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_sshLogin_appComponent_6_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_terminalComponent_xtermJs_7_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="terminalComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="xtermJs_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_terminalComponent_xtermJs_7_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_terminalComponent_xtermJs_7_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_backendServer_8_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="backendServer_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_backendServer_8_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Emits/Listens Events" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_backendServer_8_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_appComponent_appComponent_9_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)" target="appComponent_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appComponent_appComponent_9_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)_label" value="Manages State" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_appComponent_appComponent_9_69983580b1be33b446c4ee837bdf434c_section_section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-report-section-69983580b1be33b446c4ee837bdf434c-2-to-section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-69983580b1be33b446c4ee837bdf434c-2" target="section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-69983580b1be33b446c4ee837bdf434c-2-to-section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-report-section-69983580b1be33b446c4ee837bdf434c-2-to-section-diagram-69983580b1be33b446c4ee837bdf434c-Core-Application-Component:-[App.jsx](c:/Users/<USER>/Desktop/bandit-wargame-gui-complete-final/bandit-gui-app/frontend/bandit-frontend/src/App.jsx)-wrapper">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-search-result-69983580b1be33b446c4ee837bdf434c-to-report-section-69983580b1be33b446c4ee837bdf434c-0" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="search-result-69983580b1be33b446c4ee837bdf434c" target="report-section-69983580b1be33b446c4ee837bdf434c-0">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
<mxCell id="edge-report-section-69983580b1be33b446c4ee837bdf434c-0-to-report-section-69983580b1be33b446c4ee837bdf434c-1" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-69983580b1be33b446c4ee837bdf434c-0" target="report-section-69983580b1be33b446c4ee837bdf434c-1">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
<mxCell id="edge-report-section-69983580b1be33b446c4ee837bdf434c-0-to-report-section-69983580b1be33b446c4ee837bdf434c-2" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="report-section-69983580b1be33b446c4ee837bdf434c-0" target="report-section-69983580b1be33b446c4ee837bdf434c-2">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
            </root>
          </mxGraphModel>
        </diagram>
      </mxfile>