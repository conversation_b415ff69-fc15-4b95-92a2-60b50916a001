# Bandit Wargame GUI - Testing Guide

This guide provides comprehensive information on how to run and interpret tests for the Bandit Wargame GUI application.

## Table of Contents

1. [Test Types](#test-types)
2. [Running Tests](#running-tests)
3. [Test Organization](#test-organization)
4. [Writing Tests](#writing-tests)
5. [Test Reports](#test-reports)
6. [Troubleshooting](#troubleshooting)
7. [Best Practices](#best-practices)

## Test Types

The application includes several types of tests:

### 1. Unit Tests
- Test individual components in isolation
- Fast execution
- No external dependencies
- Located in `backend/bandit-backend/tests/` and `frontend/bandit-frontend/src/tests/`

### 2. Integration Tests
- Test interactions between components
- May include database or API calls
- Located in `backend/bandit-backend/tests/integration/`

### 3. API Tests
- Test the backend API endpoints
- Verify request/response handling
- Located in `backend/bandit-backend/tests/api/`

### 4. End-to-End (E2E) Tests
- Test complete user workflows
- Use Playwright for browser automation
- Located in `e2e_tests/`

### 5. Performance Tests
- Test application performance under load
- Located in `performance_tests/`

### 6. Security Tests
- Check for common security vulnerabilities
- Located in `security_audit.py`

## Running Tests

### Prerequisites

- Python 3.8+
- Node.js 14+
- npm or yarn
- Docker (for some integration tests)

### Running All Tests

Use the test runner script to run all tests:

```bash
# Run all tests
python test_runner.py

# Run specific test types
python test_runner.py --types unit integration e2e

# Run with coverage report
python test_runner.py --coverage
```

### Running Backend Tests

```bash
# Navigate to backend directory
cd backend/bandit-backend

# Install dependencies
pip install -r requirements-dev.txt

# Run all tests
pytest

# Run a specific test file
pytest tests/test_ssh_manager.py

# Run with coverage
pytest --cov=app --cov-report=html
```

### Running Frontend Tests

```bash
# Navigate to frontend directory
cd frontend/bandit-frontend

# Install dependencies
npm install

# Run all tests
npm test

# Run tests in watch mode
npm test -- --watch

# Run with coverage
npm test -- --coverage
```

### Running E2E Tests

```bash
# Install Playwright
npx playwright install

# Run all E2E tests
npx playwright test

# Run tests in headed mode
npx playwright test --headed

# Run a specific test file
npx playwright test user_workflows.test.js
```

### Running Performance Tests

```bash
# Navigate to performance tests directory
cd performance_tests

# Run performance tests
python load_testing.py

# Generate report
python generate_report.py
```

### Running Security Audit

```bash
# Run security audit
python security_audit.py

# Generate report
python security_audit.py --report security_report.html
```

## Test Organization

### Backend Tests

```
backend/
└── bandit-backend/
    └── tests/
        ├── __init__.py
        ├── conftest.py
        ├── unit/
        │   ├── test_ssh_manager.py
        │   ├── test_level_scraper.py
        │   └── test_ai_mentor.py
        ├── integration/
        │   ├── test_ssh_integration.py
        │   └── test_database.py
        └── api/
            ├── test_auth_api.py
            └── test_levels_api.py
```

### Frontend Tests

```
frontend/
└── bandit-frontend/
    └── src/
        └── tests/
            ├── __tests__/
            │   ├── components/
            │   │   ├── Terminal.test.jsx
            │   │   ├── LevelInfo.test.jsx
            │   │   └── AIMentor.test.jsx
            │   └── pages/
            │       └── Home.test.jsx
            └── setupTests.js
```

### E2E Tests

```
e2e_tests/
├── fixtures/
│   └── test-data.json
├── pages/
│   ├── base-page.js
│   ├── login-page.js
│   └── terminal-page.js
├── user_workflows.test.js
└── global-setup.js
```

## Writing Tests

### Backend Test Example

```python
# tests/unit/test_ssh_manager.py
def test_ssh_connection_success(mock_ssh_client):
    """Test successful SSH connection."""
    manager = SSHManager()
    result = manager.connect("bandit.labs.overthewire.org", 2220, "bandit0", "bandit0")
    assert result.success is True
    assert "Connected to bandit0" in result.message
```

### Frontend Test Example

```jsx
// src/tests/__tests__/components/Terminal.test.jsx
import { render, screen, fireEvent } from '@testing-library/react';
import Terminal from '../../components/Terminal';

test('displays command output', () => {
  render(<Terminal />);
  const input = screen.getByRole('textbox');
  fireEvent.change(input, { target: { value: 'ls' } });
  fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' });
  expect(screen.getByText(/command output/)).toBeInTheDocument();
});
```

### E2E Test Example

```javascript
// e2e_tests/user_workflows.test.js
const { test, expect } = require('@playwright/test');
const { LoginPage } = require('./pages/login-page');

test('user can login and see terminal', async ({ page }) => {
  const loginPage = new LoginPage(page);
  await loginPage.navigate();
  await loginPage.login('testuser', 'password');
  await expect(page).toHaveURL(/terminal/);
  await expect(page.locator('.terminal')).toBeVisible();
});
```

## Test Reports

### Generating Reports

```bash
# Generate HTML coverage report for backend
pytest --cov=app --cov-report=html

# Generate JUnit XML report for CI
pytest --junitxml=test-results/backend.xml

# Generate frontend coverage report
npm test -- --coverage --coverageReporters="json-summary"
```

### Report Locations

- Backend Coverage: `backend/bandit-backend/htmlcov/`
- Frontend Coverage: `frontend/bandit-frontend/coverage/`
- Test Results: `test-results/`
- Performance Reports: `performance_reports/`
- Security Reports: `security_reports/`

## Troubleshooting

### Common Issues

1. **Tests are failing with connection errors**
   - Ensure all required services are running
   - Check environment variables in `.env.test`
   - Verify network connectivity

2. **Frontend tests are slow**
   - Use `--runInBand` to run tests sequentially
   - Mock external API calls
   - Use `waitFor` instead of fixed timeouts

3. **E2E tests are flaky**
   - Add retries for flaky tests
   - Use `page.waitForSelector` instead of fixed timeouts
   - Run tests in headful mode for debugging

### Debugging Tests

```bash
# Run tests with debug logging
pytest -v --log-cli-level=DEBUG

# Debug a specific test
pytest tests/unit/test_ssh_manager.py -v -s

# Debug frontend tests
npm test -- --debug

# Debug E2E tests in headed mode
npx playwright test --debug
```

## Best Practices

### General

- Write small, focused tests
- Test one thing per test case
- Use descriptive test names
- Keep tests independent and isolated
- Avoid testing implementation details

### Backend

- Use fixtures for test data
- Mock external services
- Test edge cases and error conditions
- Use parameterized tests for similar scenarios

### Frontend

- Test component rendering
- Test user interactions
- Use test IDs instead of CSS selectors
- Mock API responses

### E2E

- Test critical user journeys
- Use page object model
- Run tests in parallel when possible
- Clean up test data after tests

### Performance

- Set performance budgets
- Monitor test execution time
- Run performance tests in a stable environment
- Compare results against baselines

## Continuous Integration

### GitHub Actions

Example workflow:

```yaml
name: CI

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt
    
    - name: Run tests
      run: |
        pytest --cov=app --cov-report=xml
    
    - name: Upload coverage
      uses: codecov/codecov-action@v1
      with:
        file: ./coverage.xml
        flags: unittests
```

### Environment Variables

Create a `.env.test` file with test-specific configurations:

```env
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_db
TESTING=True
DEBUG=False
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
