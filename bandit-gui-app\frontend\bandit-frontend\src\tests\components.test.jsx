import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, BrowserRouter as Router } from 'react-router-dom';
import { io } from 'socket.io-client';
import config from '../config';

// Mock socket.io-client
jest.mock('socket.io-client');

// Mock components
import Terminal from '../components/Terminal';
import LevelInfo from '../components/LevelInfo';
import AIMentor from '../components/AIMentor';
import App from '../App';

// Mock the WebSocket connection
const mockEmit = jest.fn();
const mockOn = jest.fn((event, callback) => {
  // Store the callback to be called later in tests
  if (event === 'connect') {
    process.nextTick(() => callback()); // Use nextTick to simulate async behavior
  }
  return { on: mockOn }; // Allow chaining of .on() calls
});

const mockDisconnect = jest.fn();

beforeEach(() => {
  // Reset all mocks before each test
  jest.clearAllMocks();
  
  // Reset the mock implementation for io()
  io.mockImplementation(() => ({
    emit: mockEmit,
    on: mockOn,
    disconnect: mockDisconnect,
    connected: true,
    id: 'test-socket-id'
  }));
});

// Mock the xterm.js Terminal class
const MockTerminalInstance = {
  write: jest.fn(),
  loadAddon: jest.fn(),
  open: jest.fn(),
  focus: jest.fn(),
  onData: jest.fn(),
  onKey: jest.fn(),
  resize: jest.fn(),
  dispose: jest.fn()
};

// Mock the FitAddon
const MockFitAddonInstance = {
  fit: jest.fn()
};

// Mock xterm and xterm-addon-fit
jest.mock('xterm', () => ({
  Terminal: jest.fn().mockImplementation(() => MockTerminalInstance)
}));

jest.mock('xterm-addon-fit', () => ({
  FitAddon: jest.fn().mockImplementation(() => MockFitAddonInstance)
}));

describe('Terminal Component', () => {
  const defaultProps = {
    sessionId: 'test-session-123',
    onConnect: jest.fn(),
    onDisconnect: jest.fn(),
    isConnected: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders terminal container', () => {
    render(<Terminal {...defaultProps} />);
    expect(screen.getByTestId('terminal-container')).toBeInTheDocument();
  });

  test('initializes terminal on mount', () => {
    render(<Terminal {...defaultProps} />);
    const { Terminal } = require('xterm');
    expect(Terminal).toHaveBeenCalledTimes(1);
  });

  test('connects to WebSocket with correct URL when sessionId is provided', () => {
    render(<Terminal {...defaultProps} isConnected={true} />);
    expect(io).toHaveBeenCalledWith(expect.stringMatching(/^\/ws\/ssh\/test-session-\d+$/));
  });

  test('sets up event handlers for socket connection', () => {
    render(<Terminal {...defaultProps} isConnected={true} />);
    
    // Verify socket event handlers are set up
    expect(mockOn).toHaveBeenCalledWith('connect', expect.any(Function));
    expect(mockOn).toHaveBeenCalledWith('disconnect', expect.any(Function));
    expect(mockOn).toHaveBeenCalledWith('data', expect.any(Function));
    expect(mockOn).toHaveBeenCalledWith('error', expect.any(Function));
  });

  test('calls onConnect when socket connects', () => {
    const onConnect = jest.fn();
    render(<Terminal {...defaultProps} onConnect={onConnect} isConnected={true} />);
    
    // Simulate socket connection
    const connectHandler = mockOn.mock.calls.find(call => call[0] === 'connect')[1];
    connectHandler();
    
    expect(onConnect).toHaveBeenCalled();
  });

  test('calls onDisconnect when socket disconnects', () => {
    const onDisconnect = jest.fn();
    render(<Terminal {...defaultProps} onDisconnect={onDisconnect} isConnected={true} />);
    
    // Simulate socket disconnection
    const disconnectHandler = mockOn.mock.calls.find(call => call[0] === 'disconnect')[1];
    disconnectHandler();
    
    expect(onDisconnect).toHaveBeenCalled();
  });

  test('handles window resize', () => {
    render(<Terminal {...defaultProps} />);
    window.dispatchEvent(new Event('resize'));
    const { FitAddon } = require('xterm-addon-fit');
    expect(MockFitAddonInstance.fit).toHaveBeenCalled();
  });
});

describe('LevelInfo Component', () => {
  const mockLevelData = {
    level: 0,
    title: 'Bandit Level 0',
    goal: 'Find the password for Level 1',
    commands: ['ls', 'cat'],
    reading_materials: ['man ls', 'man cat']
  };

  test('displays loading state', () => {
    render(<LevelInfo levelId="0" />);
    expect(screen.getByText('Loading level information...')).toBeInTheDocument();
  });

  test('displays level information when loaded', async () => {
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockLevelData),
      })
    );

    render(<LevelInfo levelId="0" />);
    
    await waitFor(() => {
      expect(screen.getByText('Bandit Level 0')).toBeInTheDocument();
      expect(screen.getByText('Find the password for Level 1')).toBeInTheDocument();
      expect(screen.getByText('ls')).toBeInTheDocument();
      expect(screen.getByText('man ls')).toBeInTheDocument();
    });
  });

  test('handles fetch error', async () => {
    global.fetch = jest.fn(() =>
      Promise.reject(new Error('Failed to fetch'))
    );

    render(<LevelInfo levelId="0" />);
    
    await waitFor(() => {
      expect(screen.getByText('Error loading level information')).toBeInTheDocument();
    });
  });
});

describe('AIMentor Component', () => {
  const defaultProps = {
    sessionId: 'test-session-123',
    currentLevel: 0
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders chat interface', () => {
    render(<AIMentor {...defaultProps} />);
    expect(screen.getByPlaceholderText('Type your message...')).toBeInTheDocument();
    expect(screen.getByText('Send')).toBeInTheDocument();
    expect(screen.getByText('Get Hint')).toBeInTheDocument();
  });

  test('sends message when form is submitted', async () => {
    render(<AIMentor {...defaultProps} />);
    
    const input = screen.getByPlaceholderText('Type your message...');
    const button = screen.getByText('Send');
    
    fireEvent.change(input, { target: { value: 'Hello, AI' } });
    fireEvent.click(button);
    
    expect(screen.getByText('You: Hello, AI')).toBeInTheDocument();
  });

  test('requests hint when hint button is clicked', async () => {
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ hint: 'Try using the ls command' }),
      })
    );

    render(<AIMentor {...defaultProps} />);
    
    const hintButton = screen.getByText('Get Hint');
    fireEvent.click(hintButton);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/mentor/hint/0');
    });
  });
});

describe('App', () => {
  it('renders without crashing', () => {
    render(
      <MemoryRouter>
        <App />
      </MemoryRouter>
    );
  });

  it('renders with initial route', () => {
    const testRoute = '/some-route';
    render(
      <MemoryRouter initialEntries={[testRoute]}>
        <App />
      </MemoryRouter>
    );
    // Add assertions for your route-specific content
  });

  it('handles navigation between tabs', () => {
    render(
      <MemoryRouter>
        <App />
      </MemoryRouter>
    );
    
    expect(screen.getByText('Bandit Wargame GUI')).toBeInTheDocument();
    expect(screen.getByText('Terminal')).toBeInTheDocument();
    expect(screen.getByText('Level Info')).toBeInTheDocument();
    expect(screen.getByText('AI Mentor')).toBeInTheDocument();
  });
  test('navigates between tabs', () => {
    render(
      <Router>
        <App />
      </Router>
    );
    
    const terminalTab = screen.getByText('Terminal');
    const levelInfoTab = screen.getByText('Level Info');
    const aiMentorTab = screen.getByText('AI Mentor');
    
    fireEvent.click(levelInfoTab);
    expect(screen.getByText('Level Information')).toBeInTheDocument();
    
    fireEvent.click(aiMentorTab);
    expect(screen.getByPlaceholderText('Type your message...')).toBeInTheDocument();
    
    fireEvent.click(terminalTab);
    expect(screen.getByTestId('terminal-container')).toBeInTheDocument();
  });
});
